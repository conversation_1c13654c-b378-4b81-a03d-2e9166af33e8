import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Check for expired subscriptions and update status
export async function checkSubscriptionExpiry() {
  try {
    console.log('Running subscription expiry check...');
    
    const expiredSubscriptions = await prisma.subscription.updateMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          lt: new Date()
        }
      },
      data: {
        status: 'EXPIRED'
      }
    });

    console.log(`Updated ${expiredSubscriptions.count} expired subscriptions`);
  } catch (error) {
    console.error('Error checking subscription expiry:', error);
  }
}

// Check student pacing and send warnings
export async function checkStudentPacing() {
  try {
    console.log('Running student pacing check...');
    
    const activeSubscriptions = await prisma.subscription.findMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          gt: new Date()
        }
      },
      include: {
        course: {
          include: {
            modules: {
              include: {
                lessons: true
              }
            }
          }
        },
        progress: true
      }
    });

    let warningsGenerated = 0;

    for (const subscription of activeSubscriptions) {
      const course = subscription.course;
      const totalModules = course.modules.length;
      
      // Calculate expected progress
      const daysPassed = Math.floor(
        (new Date().getTime() - subscription.startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      const expectedModules = Math.floor((daysPassed / course.durationDays) * totalModules);
      
      // Calculate actual progress
      const completedModules = course.modules.filter(module => 
        module.lessons.every(lesson => 
          subscription.progress.some(p => p.lessonId === lesson.id)
        )
      ).length;

      // Check if behind schedule
      if (completedModules < expectedModules && daysPassed > 0) {
        // Here you would typically send a notification/email
        // For now, we'll just log it
        console.log(`Student ${subscription.studentId} is behind in course ${course.title}`);
        console.log(`Expected: ${expectedModules} modules, Completed: ${completedModules} modules`);
        warningsGenerated++;
      }
    }

    console.log(`Generated ${warningsGenerated} pacing warnings`);
  } catch (error) {
    console.error('Error checking student pacing:', error);
  }
}

// Generate certificates for completed courses
export async function generateCertificates() {
  try {
    console.log('Checking for completed courses to generate certificates...');
    
    const completedSubscriptions = await prisma.subscription.findMany({
      where: {
        status: 'ACTIVE',
        progress: 100
      },
      include: {
        course: true
      }
    });

    for (const subscription of completedSubscriptions) {
      // Check if certificate already exists
      const existingCertificate = await prisma.certificate.findUnique({
        where: {
          studentId_courseId: {
            studentId: subscription.studentId,
            courseId: subscription.courseId
          }
        }
      });

      if (!existingCertificate) {
        // Generate verification code
        const verificationCode = generateVerificationCode();
        
        await prisma.certificate.create({
          data: {
            studentId: subscription.studentId,
            courseId: subscription.courseId,
            verificationCode,
          }
        });

        console.log(`Generated certificate for student ${subscription.studentId} in course ${subscription.course.title}`);
      }
    }
  } catch (error) {
    console.error('Error generating certificates:', error);
  }
}

function generateVerificationCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 12; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}