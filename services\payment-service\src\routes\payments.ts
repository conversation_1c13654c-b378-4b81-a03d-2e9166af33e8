import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { AppError } from '../middleware/errorHandler';
import xenditService from '../services/xenditService';
import axios from 'axios';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/payments/create-invoice - Create payment invoice
router.post('/create-invoice', [
  body('courseId').isString().notEmpty(),
  body('amount').isNumeric().custom(value => value > 0),
  body('currency').optional().isString().isLength({ min: 3, max: 3 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { courseId, amount, currency = 'IDR' } = req.body;
    const studentId = req.user!.id;

    // Get course details
    const courseResponse = await axios.get(
      `${process.env.COURSE_SERVICE_URL}/api/courses/${courseId}`,
      {
        headers: { Authorization: req.headers.authorization }
      }
    );

    if (!courseResponse.data.success) {
      throw new AppError('Course not found', 404);
    }

    const course = courseResponse.data.data.course;

    // Check if user already has active subscription
    const existingSubscription = await prisma.subscription.findUnique({
      where: {
        studentId_courseId: {
          studentId,
          courseId,
        }
      }
    });

    if (existingSubscription && existingSubscription.status === 'ACTIVE') {
      throw new AppError('You already have an active subscription for this course', 400);
    }

    // Create payment intent
    const externalId = `course_${courseId}_student_${studentId}_${Date.now()}`;
    
    const paymentIntent = await prisma.paymentIntent.create({
      data: {
        studentId,
        courseId,
        amount,
        currency,
      }
    });

    // Create Xendit invoice
    const xenditInvoice = await xenditService.createInvoice({
      external_id: externalId,
      amount: parseFloat(amount.toString()),
      payer_email: req.user!.email,
      description: `Time Academy - ${course.title}`,
      success_redirect_url: `${process.env.FRONTEND_URL}/payment/success`,
      failure_redirect_url: `${process.env.FRONTEND_URL}/payment/failed`,
      currency,
      items: [{
        name: course.title,
        quantity: 1,
        price: parseFloat(amount.toString()),
        category: course.category,
      }],
    });

    // Update payment intent with Xendit details
    const updatedPaymentIntent = await prisma.paymentIntent.update({
      where: { id: paymentIntent.id },
      data: {
        xenditInvoiceId: xenditInvoice.id,
        invoiceUrl: xenditInvoice.invoice_url,
        expiresAt: new Date(xenditInvoice.expiry_date),
        metadata: {
          externalId,
          courseTitle: course.title,
          courseCategory: course.category,
        },
      }
    });

    res.json({
      success: true,
      data: {
        paymentIntent: updatedPaymentIntent,
        invoiceUrl: xenditInvoice.invoice_url,
        expiresAt: xenditInvoice.expiry_date,
      },
      message: 'Payment invoice created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/payments/:id - Get payment details
router.get('/:id', [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const paymentIntent = await prisma.paymentIntent.findUnique({
      where: { id }
    });

    if (!paymentIntent) {
      throw new AppError('Payment not found', 404);
    }

    // Check if user owns this payment
    if (paymentIntent.studentId !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }

    // Get latest status from Xendit if invoice exists
    if (paymentIntent.xenditInvoiceId) {
      try {
        const xenditInvoice = await xenditService.getInvoice(paymentIntent.xenditInvoiceId);
        
        // Update local status if different
        if (xenditInvoice.status !== paymentIntent.status) {
          const statusMap: { [key: string]: string } = {
            'PENDING': 'PENDING',
            'PAID': 'COMPLETED',
            'SETTLED': 'COMPLETED',
            'EXPIRED': 'EXPIRED',
          };

          const newStatus = statusMap[xenditInvoice.status] || paymentIntent.status;
          
          await prisma.paymentIntent.update({
            where: { id },
            data: { status: newStatus as any }
          });

          paymentIntent.status = newStatus as any;
        }
      } catch (error) {
        console.error('Error fetching Xendit invoice:', error);
      }
    }

    res.json({
      success: true,
      data: { paymentIntent }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/payments - Get user's payment history
router.get('/', [
  query('status').optional().isIn(['PENDING', 'COMPLETED', 'FAILED', 'EXPIRED', 'REFUNDED']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    const studentId = req.user!.id;

    const where: any = { studentId };
    if (status) where.status = status;

    const [payments, total] = await Promise.all([
      prisma.paymentIntent.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.paymentIntent.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        payments,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/payments/:id/cancel - Cancel payment
router.post('/:id/cancel', [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const paymentIntent = await prisma.paymentIntent.findUnique({
      where: { id }
    });

    if (!paymentIntent) {
      throw new AppError('Payment not found', 404);
    }

    // Check if user owns this payment
    if (paymentIntent.studentId !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }

    if (paymentIntent.status !== 'PENDING') {
      throw new AppError('Can only cancel pending payments', 400);
    }

    // Expire invoice in Xendit
    if (paymentIntent.xenditInvoiceId) {
      try {
        await xenditService.expireInvoice(paymentIntent.xenditInvoiceId);
      } catch (error) {
        console.error('Error expiring Xendit invoice:', error);
      }
    }

    // Update payment status
    await prisma.paymentIntent.update({
      where: { id },
      data: { 
        status: 'EXPIRED',
        failureReason: 'Cancelled by user',
      }
    });

    res.json({
      success: true,
      message: 'Payment cancelled successfully'
    });
  } catch (error) {
    next(error);
  }
});

export default router;