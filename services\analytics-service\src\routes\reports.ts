import { Router, Response, NextFunction } from "express";
import { query, validationResult } from "express-validator";
import { PrismaClient } from "@prisma/client";
import {
  authenticateToken,
  requireRole,
  AuthenticatedRequest,
} from "../shared/middleware/auth";
import { UserRole } from "../shared/types";
import axios from "axios";

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: "Validation failed",
      details: errors.array(),
    });
  }
  next();
};

// GET /api/reports/students - Student progress reports
router.get(
  "/students",
  requireRole([UserRole.ADMIN, UserRole.TUTOR]),
  [
    query("courseId").optional().isString(),
    query("status").optional().isIn(["ACTIVE", "TERMINATED", "COMPLETED"]),
    query("dateFrom").optional().isISO8601(),
    query("dateTo").optional().isISO8601(),
    query("page").optional().isInt({ min: 1 }).toInt(),
    query("limit").optional().isInt({ min: 1, max: 100 }).toInt(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const {
        courseId,
        status,
        dateFrom,
        dateTo,
        page = 1,
        limit = 20,
      } = req.query;

      const pageNum = parseInt(page as string) || 1;
      const limitNum = parseInt(limit as string) || 20;

      // Get student data from course service
      const courseServiceUrl =
        process.env.COURSE_SERVICE_URL || "http://course-service:3002";

      const params = new URLSearchParams();
      if (courseId) params.append("courseId", courseId as string);
      if (status) params.append("status", status as string);
      if (dateFrom) params.append("dateFrom", dateFrom as string);
      if (dateTo) params.append("dateTo", dateTo as string);
      params.append("page", page.toString());
      params.append("limit", limit.toString());

      const response = await axios.get(
        `${courseServiceUrl}/api/progress/report?${params}`,
        {
          headers: { Authorization: req.headers.authorization },
        }
      );

      if (!response.data.success) {
        throw new Error("Failed to fetch student progress data");
      }

      const studentData = response.data.data;

      // Enhance with analytics data
      const enhancedData = await Promise.all(
        studentData.students.map(async (student: any) => {
          const analytics = await prisma.studentAnalytics.findFirst({
            where: { studentId: student.id },
            orderBy: { date: "desc" },
          });

          return {
            ...student,
            analytics: analytics || {
              totalWatchTime: 0,
              assignmentsCompleted: 0,
              assignmentsPassed: 0,
              assignmentsFailed: 0,
              averageScore: 0,
            },
          };
        })
      );

      res.json({
        success: true,
        data: {
          students: enhancedData,
          pagination: studentData.pagination,
          summary: {
            totalStudents: studentData.pagination.total,
            activeStudents: enhancedData.filter(
              (s: any) => s.status === "ACTIVE"
            ).length,
            terminatedStudents: enhancedData.filter(
              (s: any) => s.status === "TERMINATED"
            ).length,
            completedStudents: enhancedData.filter(
              (s: any) => s.status === "COMPLETED"
            ).length,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/reports/courses - Course performance reports
router.get(
  "/courses",
  requireRole([UserRole.ADMIN, UserRole.TUTOR]),
  [
    query("tutorId").optional().isString(),
    query("category").optional().isString(),
    query("dateFrom").optional().isISO8601(),
    query("dateTo").optional().isISO8601(),
    query("page").optional().isInt({ min: 1 }).toInt(),
    query("limit").optional().isInt({ min: 1, max: 100 }).toInt(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const {
        tutorId,
        category,
        dateFrom,
        dateTo,
        page = 1,
        limit = 20,
      } = req.query;

      const pageNum = parseInt(page as string) || 1;
      const limitNum = parseInt(limit as string) || 20;

      const startDate = dateFrom
        ? new Date(dateFrom as string)
        : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const endDate = dateTo ? new Date(dateTo as string) : new Date();

      // Get course analytics
      const where: any = {
        date: {
          gte: startDate,
          lte: endDate,
        },
      };

      const courseAnalytics = await prisma.courseAnalytics.findMany({
        where,
        orderBy: { enrollments: "desc" },
      });

      // Group by course and calculate totals
      const courseMap = new Map();

      courseAnalytics.forEach((analytics) => {
        const courseId = analytics.courseId;
        if (!courseMap.has(courseId)) {
          courseMap.set(courseId, {
            courseId,
            totalEnrollments: 0,
            totalRevenue: 0,
            averageProgress: 0,
            completionRate: 0,
            terminationRate: 0,
            dataPoints: 0,
          });
        }

        const course = courseMap.get(courseId);
        course.totalEnrollments += analytics.enrollments;
        course.totalRevenue += Number(analytics.revenue);
        course.averageProgress += Number(analytics.averageProgress);
        course.completionRate +=
          (analytics.completedStudents / Math.max(analytics.enrollments, 1)) *
          100;
        course.terminationRate +=
          (analytics.terminatedStudents / Math.max(analytics.enrollments, 1)) *
          100;
        course.dataPoints += 1;
      });

      // Calculate averages and get course details
      const courseServiceUrl =
        process.env.COURSE_SERVICE_URL || "http://course-service:3002";
      const courses = [];

      for (const [courseId, data] of courseMap) {
        try {
          const courseResponse = await axios.get(
            `${courseServiceUrl}/api/courses/${courseId}`,
            {
              headers: { Authorization: req.headers.authorization },
            }
          );

          if (courseResponse.data.success) {
            const courseInfo = courseResponse.data.data.course;

            courses.push({
              ...courseInfo,
              analytics: {
                totalEnrollments: data.totalEnrollments,
                totalRevenue: data.totalRevenue,
                averageProgress:
                  data.dataPoints > 0
                    ? data.averageProgress / data.dataPoints
                    : 0,
                completionRate:
                  data.dataPoints > 0
                    ? data.completionRate / data.dataPoints
                    : 0,
                terminationRate:
                  data.dataPoints > 0
                    ? data.terminationRate / data.dataPoints
                    : 0,
              },
            });
          }
        } catch (error) {
          console.error(`Error fetching course ${courseId}:`, error);
        }
      }

      // Apply filters
      let filteredCourses = courses;
      if (tutorId) {
        filteredCourses = filteredCourses.filter(
          (course) => course.tutorId === tutorId
        );
      }
      if (category) {
        filteredCourses = filteredCourses.filter(
          (course) => course.category === category
        );
      }

      // Pagination
      const offset = (pageNum - 1) * limitNum;
      const paginatedCourses = filteredCourses.slice(offset, offset + limitNum);

      res.json({
        success: true,
        data: {
          courses: paginatedCourses,
          pagination: {
            total: filteredCourses.length,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(filteredCourses.length / limitNum),
          },
          summary: {
            totalCourses: filteredCourses.length,
            totalEnrollments: filteredCourses.reduce(
              (sum, course) => sum + course.analytics.totalEnrollments,
              0
            ),
            totalRevenue: filteredCourses.reduce(
              (sum, course) => sum + course.analytics.totalRevenue,
              0
            ),
            averageCompletionRate:
              filteredCourses.length > 0
                ? filteredCourses.reduce(
                    (sum, course) => sum + course.analytics.completionRate,
                    0
                  ) / filteredCourses.length
                : 0,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/reports/revenue - Revenue reports
router.get(
  "/revenue",
  requireRole([UserRole.ADMIN]),
  [
    query("dateFrom").optional().isISO8601(),
    query("dateTo").optional().isISO8601(),
    query("groupBy").optional().isIn(["day", "week", "month"]),
    query("courseId").optional().isString(),
    query("tutorId").optional().isString(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const {
        dateFrom,
        dateTo,
        groupBy = "day",
        courseId,
        tutorId,
      } = req.query;

      const startDate = dateFrom
        ? new Date(dateFrom as string)
        : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const endDate = dateTo ? new Date(dateTo as string) : new Date();

      const where: any = {
        date: {
          gte: startDate,
          lte: endDate,
        },
      };

      if (courseId) where.courseId = courseId;
      if (tutorId) where.tutorId = tutorId;

      const revenueData = await prisma.revenueAnalytics.findMany({
        where,
        orderBy: { date: "asc" },
      });

      // Group data based on groupBy parameter
      const groupedData = groupRevenueData(revenueData, groupBy as string);

      // Calculate summary statistics
      const summary = revenueData.reduce(
        (acc, item) => ({
          totalSubscriptions: acc.totalSubscriptions + item.subscriptions,
          grossRevenue: acc.grossRevenue + Number(item.grossRevenue),
          platformFee: acc.platformFee + Number(item.platformFee),
          tutorEarnings: acc.tutorEarnings + Number(item.tutorEarnings),
          refunds: acc.refunds + Number(item.refunds),
          netRevenue: acc.netRevenue + Number(item.netRevenue),
        }),
        {
          totalSubscriptions: 0,
          grossRevenue: 0,
          platformFee: 0,
          tutorEarnings: 0,
          refunds: 0,
          netRevenue: 0,
        }
      );

      res.json({
        success: true,
        data: {
          summary,
          timeline: groupedData,
          period: {
            from: startDate,
            to: endDate,
            groupBy,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/reports/system-health - System health report
router.get(
  "/system-health",
  requireRole([UserRole.ADMIN]),
  [
    query("hours").optional().isInt({ min: 1, max: 168 }).toInt(), // Max 1 week
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { hours = 24 } = req.query;
      const hoursNum = parseInt(hours as string) || 24;

      const startTime = new Date();
      startTime.setHours(startTime.getHours() - hoursNum);

      const healthData = await prisma.systemHealth.findMany({
        where: {
          timestamp: {
            gte: startTime,
          },
        },
        orderBy: { timestamp: "desc" },
      });

      // Calculate uptime percentages
      const totalChecks = healthData.length;
      const serviceUptime = {
        auth: calculateUptime(healthData, "authServiceStatus"),
        course: calculateUptime(healthData, "courseServiceStatus"),
        payment: calculateUptime(healthData, "paymentServiceStatus"),
        analytics: calculateUptime(healthData, "analyticsServiceStatus"),
      };

      // Get latest health status
      const latestHealth = healthData[0] || null;

      res.json({
        success: true,
        data: {
          current: latestHealth,
          uptime: serviceUptime,
          timeline: healthData.slice(0, 100), // Last 100 checks
          period: {
            hours,
            totalChecks,
            from: startTime,
            to: new Date(),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// Helper functions
function groupRevenueData(data: any[], groupBy: string) {
  const grouped = new Map();

  data.forEach((item) => {
    let key: string;
    const date = new Date(item.date);

    switch (groupBy) {
      case "week":
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        key = weekStart.toISOString().split("T")[0];
        break;
      case "month":
        key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
          2,
          "0"
        )}`;
        break;
      default: // day
        key = item.date.toISOString().split("T")[0];
    }

    if (!grouped.has(key)) {
      grouped.set(key, {
        period: key,
        subscriptions: 0,
        grossRevenue: 0,
        platformFee: 0,
        tutorEarnings: 0,
        refunds: 0,
        netRevenue: 0,
      });
    }

    const group = grouped.get(key);
    group.subscriptions += item.subscriptions;
    group.grossRevenue += Number(item.grossRevenue);
    group.platformFee += Number(item.platformFee);
    group.tutorEarnings += Number(item.tutorEarnings);
    group.refunds += Number(item.refunds);
    group.netRevenue += Number(item.netRevenue);
  });

  return Array.from(grouped.values()).sort((a, b) =>
    a.period.localeCompare(b.period)
  );
}

function calculateUptime(healthData: any[], serviceField: string): number {
  if (healthData.length === 0) return 0;

  const upCount = healthData.filter(
    (item) => item[serviceField] === "healthy"
  ).length;
  return Math.round((upCount / healthData.length) * 100);
}

export default router;
