generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model PaymentIntent {
  id               String        @id @default(cuid())
  studentId        String        @map("student_id")
  courseId         String        @map("course_id")
  amount           Decimal       @db.Decimal(10, 2)
  currency         String        @default("IDR")
  status           PaymentStatus @default(PENDING)
  xenditInvoiceId  String?       @unique @map("xendit_invoice_id")
  xenditPaymentId  String?       @map("xendit_payment_id")
  invoiceUrl       String?       @map("invoice_url")
  expiresAt        DateTime?     @map("expires_at")
  paidAt           DateTime?     @map("paid_at")
  failureReason    String?       @map("failure_reason")
  metadata         Json?
  createdAt        DateTime      @default(now()) @map("created_at")
  updatedAt        DateTime      @updatedAt @map("updated_at")

  @@map("payment_intents")
}

model Subscription {
  id           String             @id @default(cuid())
  studentId    String             @map("student_id")
  courseId     String             @map("course_id")
  paymentId    String?            @map("payment_id")
  status       SubscriptionStatus @default(ACTIVE)
  startDate    DateTime           @map("start_date")
  endDate      DateTime           @map("end_date")
  autoRenew    Boolean            @default(false) @map("auto_renew")
  cancelledAt  DateTime?          @map("cancelled_at")
  cancelReason String?            @map("cancel_reason")
  createdAt    DateTime           @default(now()) @map("created_at")
  updatedAt    DateTime           @updatedAt @map("updated_at")

  @@unique([studentId, courseId])
  @@map("subscriptions")
}

model RefundRequest {
  id            String       @id @default(cuid())
  paymentId     String       @map("payment_id")
  studentId     String       @map("student_id")
  amount        Decimal      @db.Decimal(10, 2)
  reason        String
  status        RefundStatus @default(PENDING)
  processedAt   DateTime?    @map("processed_at")
  processedBy   String?      @map("processed_by")
  xenditRefundId String?     @map("xendit_refund_id")
  createdAt     DateTime     @default(now()) @map("created_at")
  updatedAt     DateTime     @updatedAt @map("updated_at")

  @@map("refund_requests")
}

model WebhookLog {
  id          String   @id @default(cuid())
  provider    String   // 'xendit'
  eventType   String   @map("event_type")
  eventId     String?  @map("event_id")
  payload     Json
  processed   Boolean  @default(false)
  error       String?
  createdAt   DateTime @default(now()) @map("created_at")

  @@map("webhook_logs")
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  EXPIRED
  REFUNDED
}

enum SubscriptionStatus {
  ACTIVE
  EXPIRED
  CANCELLED
  TERMINATED
}

enum RefundStatus {
  PENDING
  APPROVED
  REJECTED
  PROCESSED
}