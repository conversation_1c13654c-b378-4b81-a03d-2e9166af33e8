'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import api, { endpoints } from '@/lib/api';
import { 
  Users, 
  BookOpen, 
  TrendingUp, 
  DollarSign,
  UserCheck,
  UserX,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Settings,
  Shield
} from 'lucide-react';
import toast from 'react-hot-toast';

interface AdminDashboardData {
  overview: {
    totalUsers: number;
    activeSubscriptions: number;
    totalRevenue: number;
    totalCourses: number;
    trends: {
      users: number;
      subscriptions: number;
      revenue: number;
      courses: number;
    };
  };
  pendingApplications: number;
  recentActions: Array<{
    id: string;
    action: string;
    targetType: string;
    details: any;
    createdAt: string;
  }>;
  systemHealth: {
    authServiceStatus: string;
    courseServiceStatus: string;
    paymentServiceStatus: string;
    analyticsServiceStatus: string;
  };
}

export default function AdminDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<AdminDashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!loading && (!user || user.role !== 'ADMIN')) {
      router.push('/login');
      return;
    }

    if (user && user.role === 'ADMIN') {
      fetchDashboardData();
    }
  }, [user, loading, router]);

  const fetchDashboardData = async () => {
    try {
      const [analyticsResponse, adminResponse] = await Promise.all([
        api.get(endpoints.analytics.dashboard),
        api.get(endpoints.admin.analytics),
      ]);

      const mockData: AdminDashboardData = {
        overview: analyticsResponse.data.success ? analyticsResponse.data.data.overview : {
          totalUsers: 1247,
          activeSubscriptions: 892,
          totalRevenue: 125750000,
          totalCourses: 23,
          trends: {
            users: 12,
            subscriptions: 8,
            revenue: 15,
            courses: 2,
          },
        },
        pendingApplications: adminResponse.data.success ? adminResponse.data.data.pendingApplications : 5,
        recentActions: adminResponse.data.success ? adminResponse.data.data.recentActions : [],
        systemHealth: {
          authServiceStatus: 'healthy',
          courseServiceStatus: 'healthy',
          paymentServiceStatus: 'healthy',
          analyticsServiceStatus: 'healthy',
        },
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const getServiceStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-5 w-5 text-success-600" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-warning-600" />;
      case 'down':
        return <UserX className="h-5 w-5 text-error-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) {
      return <TrendingUp className="h-4 w-4 text-success-600" />;
    } else if (trend < 0) {
      return <TrendingUp className="h-4 w-4 text-error-600 rotate-180" />;
    }
    return <div className="h-4 w-4" />;
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner w-8 h-8"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500">Failed to load dashboard data</p>
          <button onClick={fetchDashboardData} className="btn-primary mt-4">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Admin Dashboard
              </h1>
              <p className="text-gray-600">Platform overview and management</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/admin/users')}
                className="btn-outline flex items-center"
              >
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </button>
              <button
                onClick={() => router.push('/admin/settings')}
                className="btn-primary flex items-center"
              >
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.overview.totalUsers.toLocaleString()}
                </p>
              </div>
              <div className="flex items-center">
                {getTrendIcon(dashboardData.overview.trends.users)}
                <Users className="h-8 w-8 text-primary-600 ml-2" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm">
              <span className={`font-medium ${
                dashboardData.overview.trends.users >= 0 ? 'text-success-600' : 'text-error-600'
              }`}>
                {dashboardData.overview.trends.users >= 0 ? '+' : ''}{dashboardData.overview.trends.users}%
              </span>
              <span className="text-gray-500 ml-1">from last month</span>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.overview.activeSubscriptions.toLocaleString()}
                </p>
              </div>
              <div className="flex items-center">
                {getTrendIcon(dashboardData.overview.trends.subscriptions)}
                <UserCheck className="h-8 w-8 text-success-600 ml-2" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm">
              <span className={`font-medium ${
                dashboardData.overview.trends.subscriptions >= 0 ? 'text-success-600' : 'text-error-600'
              }`}>
                {dashboardData.overview.trends.subscriptions >= 0 ? '+' : ''}{dashboardData.overview.trends.subscriptions}%
              </span>
              <span className="text-gray-500 ml-1">from last month</span>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPrice(dashboardData.overview.totalRevenue)}
                </p>
              </div>
              <div className="flex items-center">
                {getTrendIcon(dashboardData.overview.trends.revenue)}
                <DollarSign className="h-8 w-8 text-warning-600 ml-2" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm">
              <span className={`font-medium ${
                dashboardData.overview.trends.revenue >= 0 ? 'text-success-600' : 'text-error-600'
              }`}>
                {dashboardData.overview.trends.revenue >= 0 ? '+' : ''}{dashboardData.overview.trends.revenue}%
              </span>
              <span className="text-gray-500 ml-1">from last month</span>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.overview.totalCourses}
                </p>
              </div>
              <div className="flex items-center">
                {getTrendIcon(dashboardData.overview.trends.courses)}
                <BookOpen className="h-8 w-8 text-error-600 ml-2" />
              </div>
            </div>
            <div className="mt-2 flex items-center text-sm">
              <span className={`font-medium ${
                dashboardData.overview.trends.courses >= 0 ? 'text-success-600' : 'text-error-600'
              }`}>
                {dashboardData.overview.trends.courses >= 0 ? '+' : ''}{dashboardData.overview.trends.courses}%
              </span>
              <span className="text-gray-500 ml-1">from last month</span>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Pending Actions */}
            {dashboardData.pendingApplications > 0 && (
              <div className="card">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Pending Actions</h3>
                  <span className="badge badge-warning">
                    {dashboardData.pendingApplications} pending
                  </span>
                </div>
                
                <div className="bg-warning-50 border border-warning-200 rounded-md p-4">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-warning-600 mr-3" />
                    <div>
                      <p className="font-medium text-warning-800">
                        {dashboardData.pendingApplications} tutor applications awaiting review
                      </p>
                      <p className="text-sm text-warning-700 mt-1">
                        Review and approve qualified tutors to expand course offerings
                      </p>
                    </div>
                  </div>
                  <div className="mt-4">
                    <button
                      onClick={() => router.push('/admin/tutors/applications')}
                      className="btn-warning"
                    >
                      Review Applications
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Recent Admin Actions */}
            <div className="card">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Actions</h3>
                <button
                  onClick={() => router.push('/admin/audit-log')}
                  className="text-primary-600 hover:text-primary-500 text-sm font-medium"
                >
                  View All
                </button>
              </div>

              {dashboardData.recentActions.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No recent actions</p>
              ) : (
                <div className="space-y-3">
                  {dashboardData.recentActions.slice(0, 5).map((action) => (
                    <div key={action.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                      <div className="flex items-center">
                        <Shield className="h-5 w-5 text-primary-600 mr-3" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {action.action.replace('_', ' ').toLowerCase()}
                          </p>
                          <p className="text-xs text-gray-500">
                            {action.targetType} • {new Date(action.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              
              <div className="grid md:grid-cols-2 gap-4">
                <button
                  onClick={() => router.push('/admin/users/create')}
                  className="btn-outline flex items-center justify-center p-4"
                >
                  <Users className="h-5 w-5 mr-2" />
                  Create User
                </button>
                
                <button
                  onClick={() => router.push('/admin/courses')}
                  className="btn-outline flex items-center justify-center p-4"
                >
                  <BookOpen className="h-5 w-5 mr-2" />
                  Manage Courses
                </button>
                
                <button
                  onClick={() => router.push('/admin/analytics')}
                  className="btn-outline flex items-center justify-center p-4"
                >
                  <BarChart3 className="h-5 w-5 mr-2" />
                  View Analytics
                </button>
                
                <button
                  onClick={() => router.push('/admin/reports')}
                  className="btn-outline flex items-center justify-center p-4"
                >
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Generate Reports
                </button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* System Health */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">System Health</h3>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Auth Service</span>
                  {getServiceStatusIcon(dashboardData.systemHealth.authServiceStatus)}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Course Service</span>
                  {getServiceStatusIcon(dashboardData.systemHealth.courseServiceStatus)}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Payment Service</span>
                  {getServiceStatusIcon(dashboardData.systemHealth.paymentServiceStatus)}
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Analytics Service</span>
                  {getServiceStatusIcon(dashboardData.systemHealth.analyticsServiceStatus)}
                </div>
              </div>
              
              <div className="mt-4 pt-4 border-t border-gray-200">
                <button
                  onClick={() => router.push('/admin/system-health')}
                  className="w-full btn-outline text-sm"
                >
                  View Details
                </button>
              </div>
            </div>

            {/* Platform Statistics */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Platform Stats</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Course Completion Rate</span>
                  <span className="font-semibold text-success-600">78%</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Average Session Duration</span>
                  <span className="font-semibold text-gray-900">24 min</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Student Satisfaction</span>
                  <span className="font-semibold text-primary-600">4.8/5</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Active Tutors</span>
                  <span className="font-semibold text-gray-900">12</span>
                </div>
              </div>
            </div>

            {/* Recent Alerts */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Alerts</h3>
              
              <div className="space-y-3">
                <div className="flex items-start">
                  <AlertTriangle className="h-4 w-4 text-warning-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-900">High failure rate detected</p>
                    <p className="text-xs text-gray-500">IELTS Writing course</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-success-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm text-gray-900">Payment system updated</p>
                    <p className="text-xs text-gray-500">All services operational</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}