{"name": "course-service", "version": "1.0.0", "description": "Course and Learning Management Service", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:generate": "npx prisma generate"}, "dependencies": {"@prisma/client": "^5.7.0", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.6.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "firebase-admin": "^12.7.0", "form-data": "^4.0.0", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "jest": "^29.7.0", "prisma": "^5.7.0", "ts-node-dev": "^2.0.0", "typescript": "^5.3.0"}}