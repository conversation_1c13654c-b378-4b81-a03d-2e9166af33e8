version: '3.8'

services:
  # Databases
  auth-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: auth_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - auth_db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - time-academy-network

  course-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: course_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - course_db_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - time-academy-network

  payment-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: payment_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - payment_db_data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - time-academy-network

  analytics-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: analytics_service
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    volumes:
      - analytics_db_data:/var/lib/postgresql/data
    ports:
      - "5435:5432"
    networks:
      - time-academy-network

  # Services
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - DATABASE_URL=*******************************************/auth_service
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - auth-db
    networks:
      - time-academy-network
    volumes:
      - ./services/auth-service:/app
      - /app/node_modules

  course-service:
    build:
      context: ./services/course-service
      dockerfile: Dockerfile
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=development
      - PORT=3002
      - DATABASE_URL=*********************************************/course_service
      - AUTH_SERVICE_URL=http://auth-service:3001
      - BUNNY_API_KEY=${BUNNY_API_KEY}
      - BUNNY_STREAM_API_KEY=${BUNNY_STREAM_API_KEY}
      - BUNNY_LIBRARY_ID=${BUNNY_LIBRARY_ID}
      - FRONTEND_URL=${FRONTEND_URL}
    depends_on:
      - course-db
      - auth-service
    networks:
      - time-academy-network
    volumes:
      - ./services/course-service:/app
      - /app/node_modules

  payment-service:
    build:
      context: ./services/payment-service
      dockerfile: Dockerfile
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=development
      - PORT=3003
      - DATABASE_URL=**********************************************/payment_service
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COURSE_SERVICE_URL=http://course-service:3002
      - XENDIT_SECRET_KEY=${XENDIT_SECRET_KEY}
      - XENDIT_WEBHOOK_TOKEN=${XENDIT_WEBHOOK_TOKEN}
      - FRONTEND_URL=${FRONTEND_URL}
    depends_on:
      - payment-db
      - auth-service
    networks:
      - time-academy-network
    volumes:
      - ./services/payment-service:/app
      - /app/node_modules

  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=development
      - PORT=3004
      - DATABASE_URL=************************************************/analytics_service
      - AUTH_SERVICE_URL=http://auth-service:3001
      - COURSE_SERVICE_URL=http://course-service:3002
      - PAYMENT_SERVICE_URL=http://payment-service:3003
      - FRONTEND_URL=${FRONTEND_URL}
    depends_on:
      - analytics-db
      - auth-service
    networks:
      - time-academy-network
    volumes:
      - ./services/analytics-service:/app
      - /app/node_modules

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:80
      - NEXT_PUBLIC_FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=${FIREBASE_PROJECT_ID}.firebaseapp.com
      - NEXT_PUBLIC_FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=${FIREBASE_PROJECT_ID}.appspot.com
      - NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=${FIREBASE_MESSAGING_SENDER_ID}
      - NEXT_PUBLIC_FIREBASE_APP_ID=${FIREBASE_APP_ID}
    depends_on:
      - api-gateway
    networks:
      - time-academy-network
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next

  # API Gateway (Nginx)
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "80:80"
    depends_on:
      - auth-service
      - course-service
      - payment-service
      - analytics-service
    networks:
      - time-academy-network

networks:
  time-academy-network:
    driver: bridge

volumes:
  auth_db_data:
  course_db_data:
  payment_db_data:
  analytics_db_data: