'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import api, { endpoints } from '@/lib/api';
import { 
  BookOpen, 
  Users, 
  TrendingUp, 
  Calendar,
  Plus,
  Eye,
  Edit,
  BarChart3,
  Clock,
  Award,
  AlertTriangle
} from 'lucide-react';
import toast from 'react-hot-toast';

interface TutorDashboardData {
  courses: Array<{
    id: string;
    title: string;
    category: string;
    isPublished: boolean;
    price: number;
    currency: string;
    durationDays: number;
    _count: {
      subscriptions: number;
      modules: number;
    };
    createdAt: string;
  }>;
  stats: {
    totalCourses: number;
    publishedCourses: number;
    totalStudents: number;
    totalRevenue: number;
  };
  recentActivity: Array<{
    type: string;
    message: string;
    timestamp: string;
  }>;
}

export default function TutorDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<TutorDashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!loading && (!user || user.role !== 'TUTOR')) {
      router.push('/login');
      return;
    }

    if (user && user.role === 'TUTOR') {
      fetchDashboardData();
    }
  }, [user, loading, router]);

  const fetchDashboardData = async () => {
    try {
      // Fetch tutor's courses
      const coursesResponse = await api.get(`${endpoints.courses.list}?tutorId=${user?.id}`);
      
      // Mock data for demo - in real app, this would come from analytics service
      const mockData: TutorDashboardData = {
        courses: coursesResponse.data.success ? coursesResponse.data.data.courses : [],
        stats: {
          totalCourses: 5,
          publishedCourses: 3,
          totalStudents: 127,
          totalRevenue: 15750000, // IDR
        },
        recentActivity: [
          {
            type: 'enrollment',
            message: 'New student enrolled in IELTS Preparation',
            timestamp: new Date().toISOString(),
          },
          {
            type: 'completion',
            message: 'Student completed TOEFL Writing module',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          },
        ],
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner w-8 h-8"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500">Failed to load dashboard data</p>
          <button onClick={fetchDashboardData} className="btn-primary mt-4">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {user?.displayName}!
              </h1>
              <p className="text-gray-600">Manage your courses and track student progress</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/tutor/courses/create')}
                className="btn-primary flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Course
              </button>
              <button
                onClick={() => router.push('/tutor/profile')}
                className="btn-outline"
              >
                Profile
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-primary-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Total Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.stats.totalCourses}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <Eye className="h-8 w-8 text-success-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Published</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.stats.publishedCourses}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-warning-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.stats.totalStudents}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-error-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatPrice(dashboardData.stats.totalRevenue, 'IDR')}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Courses */}
          <div className="lg:col-span-2">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Your Courses</h2>
              <button
                onClick={() => router.push('/tutor/courses')}
                className="text-primary-600 hover:text-primary-500 text-sm font-medium"
              >
                View All
              </button>
            </div>

            {dashboardData.courses.length === 0 ? (
              <div className="card text-center py-12">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No courses yet</h3>
                <p className="text-gray-500 mb-4">Create your first course to start teaching!</p>
                <button
                  onClick={() => router.push('/tutor/courses/create')}
                  className="btn-primary"
                >
                  Create Course
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {dashboardData.courses.slice(0, 5).map((course) => (
                  <div key={course.id} className="card">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 mr-3">
                            {course.title}
                          </h3>
                          <span className={`badge ${course.isPublished ? 'badge-success' : 'badge-gray'}`}>
                            {course.isPublished ? 'Published' : 'Draft'}
                          </span>
                        </div>
                        
                        <p className="text-gray-600 mb-3">{course.category}</p>
                        
                        <div className="flex items-center space-x-6 text-sm text-gray-500">
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {course._count.subscriptions} students
                          </div>
                          <div className="flex items-center">
                            <BookOpen className="h-4 w-4 mr-1" />
                            {course._count.modules} modules
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            {course.durationDays} days
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <p className="text-lg font-bold text-gray-900">
                          {formatPrice(course.price, course.currency)}
                        </p>
                        <p className="text-sm text-gray-500">
                          Created {formatDate(course.createdAt)}
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex justify-end space-x-3 mt-4 pt-4 border-t border-gray-200">
                      <button
                        onClick={() => router.push(`/tutor/courses/${course.id}/analytics`)}
                        className="btn-outline flex items-center text-sm"
                      >
                        <BarChart3 className="h-4 w-4 mr-2" />
                        Analytics
                      </button>
                      <button
                        onClick={() => router.push(`/tutor/courses/${course.id}/edit`)}
                        className="btn-outline flex items-center text-sm"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </button>
                      <button
                        onClick={() => router.push(`/courses/${course.id}`)}
                        className="btn-primary flex items-center text-sm"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Recent Activity */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
              
              {dashboardData.recentActivity.length === 0 ? (
                <p className="text-gray-500 text-sm">No recent activity</p>
              ) : (
                <div className="space-y-3">
                  {dashboardData.recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0 mt-1">
                        {activity.type === 'enrollment' && (
                          <Users className="h-4 w-4 text-success-600" />
                        )}
                        {activity.type === 'completion' && (
                          <Award className="h-4 w-4 text-primary-600" />
                        )}
                        {activity.type === 'warning' && (
                          <AlertTriangle className="h-4 w-4 text-warning-600" />
                        )}
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-gray-900">{activity.message}</p>
                        <p className="text-xs text-gray-500">
                          {formatDate(activity.timestamp)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              
              <div className="space-y-3">
                <button
                  onClick={() => router.push('/tutor/courses/create')}
                  className="w-full btn-primary flex items-center justify-center"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Course
                </button>
                
                <button
                  onClick={() => router.push('/tutor/coaching')}
                  className="w-full btn-outline flex items-center justify-center"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule Coaching
                </button>
                
                <button
                  onClick={() => router.push('/tutor/analytics')}
                  className="w-full btn-outline flex items-center justify-center"
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Analytics
                </button>
              </div>
            </div>

            {/* Performance Summary */}
            <div className="card">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">This Month</h3>
              
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">New Enrollments</span>
                  <span className="font-semibold text-success-600">+23</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Course Completions</span>
                  <span className="font-semibold text-primary-600">12</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Revenue</span>
                  <span className="font-semibold text-gray-900">
                    {formatPrice(3250000, 'IDR')}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Avg. Rating</span>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-400 fill-current mr-1" />
                    <span className="font-semibold">4.8</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}