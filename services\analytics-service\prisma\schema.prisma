generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model DailyMetrics {
  id                    String   @id @default(cuid())
  date                  DateTime @unique @db.Date
  totalUsers            Int      @default(0) @map("total_users")
  newUsers              Int      @default(0) @map("new_users")
  activeSubscriptions   Int      @default(0) @map("active_subscriptions")
  newSubscriptions      Int      @default(0) @map("new_subscriptions")
  cancelledSubscriptions Int     @default(0) @map("cancelled_subscriptions")
  terminatedSubscriptions Int    @default(0) @map("terminated_subscriptions")
  totalRevenue          Decimal  @default(0) @db.Decimal(12, 2) @map("total_revenue")
  newRevenue            Decimal  @default(0) @db.Decimal(12, 2) @map("new_revenue")
  totalCourses          Int      @default(0) @map("total_courses")
  publishedCourses      Int      @default(0) @map("published_courses")
  totalLessons          Int      @default(0) @map("total_lessons")
  totalAssignments      Int      @default(0) @map("total_assignments")
  assignmentSubmissions Int      @default(0) @map("assignment_submissions")
  passedAssignments     Int      @default(0) @map("passed_assignments")
  failedAssignments     Int      @default(0) @map("failed_assignments")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  @@map("daily_metrics")
}

model CourseAnalytics {
  id                    String   @id @default(cuid())
  courseId              String   @map("course_id")
  date                  DateTime @db.Date
  enrollments           Int      @default(0)
  activeStudents        Int      @default(0) @map("active_students")
  completedStudents     Int      @default(0) @map("completed_students")
  terminatedStudents    Int      @default(0) @map("terminated_students")
  averageProgress       Decimal  @default(0) @db.Decimal(5, 2) @map("average_progress")
  totalWatchTime        Int      @default(0) @map("total_watch_time") // in minutes
  assignmentSubmissions Int      @default(0) @map("assignment_submissions")
  assignmentPassRate    Decimal  @default(0) @db.Decimal(5, 2) @map("assignment_pass_rate")
  revenue               Decimal  @default(0) @db.Decimal(10, 2)
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  @@unique([courseId, date])
  @@map("course_analytics")
}

model TutorAnalytics {
  id                  String   @id @default(cuid())
  tutorId             String   @map("tutor_id")
  date                DateTime @db.Date
  totalCourses        Int      @default(0) @map("total_courses")
  publishedCourses    Int      @default(0) @map("published_courses")
  totalStudents       Int      @default(0) @map("total_students")
  activeStudents      Int      @default(0) @map("active_students")
  completedStudents   Int      @default(0) @map("completed_students")
  terminatedStudents  Int      @default(0) @map("terminated_students")
  averageRating       Decimal? @db.Decimal(3, 2) @map("average_rating")
  totalRevenue        Decimal  @default(0) @db.Decimal(10, 2) @map("total_revenue")
  coachingSessions    Int      @default(0) @map("coaching_sessions")
  sessionParticipants Int      @default(0) @map("session_participants")
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  @@unique([tutorId, date])
  @@map("tutor_analytics")
}

model StudentAnalytics {
  id                    String   @id @default(cuid())
  studentId             String   @map("student_id")
  date                  DateTime @db.Date
  activeCourses         Int      @default(0) @map("active_courses")
  completedCourses      Int      @default(0) @map("completed_courses")
  terminatedCourses     Int      @default(0) @map("terminated_courses")
  totalWatchTime        Int      @default(0) @map("total_watch_time") // in minutes
  assignmentsCompleted  Int      @default(0) @map("assignments_completed")
  assignmentsPassed     Int      @default(0) @map("assignments_passed")
  assignmentsFailed     Int      @default(0) @map("assignments_failed")
  averageScore          Decimal  @default(0) @db.Decimal(5, 2) @map("average_score")
  coachingSessionsJoined Int     @default(0) @map("coaching_sessions_joined")
  certificatesEarned    Int      @default(0) @map("certificates_earned")
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @updatedAt @map("updated_at")

  @@unique([studentId, date])
  @@map("student_analytics")
}

model RevenueAnalytics {
  id              String   @id @default(cuid())
  date            DateTime @db.Date
  courseId        String?  @map("course_id")
  tutorId         String?  @map("tutor_id")
  subscriptions   Int      @default(0)
  grossRevenue    Decimal  @default(0) @db.Decimal(12, 2) @map("gross_revenue")
  platformFee     Decimal  @default(0) @db.Decimal(12, 2) @map("platform_fee")
  tutorEarnings   Decimal  @default(0) @db.Decimal(12, 2) @map("tutor_earnings")
  refunds         Decimal  @default(0) @db.Decimal(12, 2)
  netRevenue      Decimal  @default(0) @db.Decimal(12, 2) @map("net_revenue")
  currency        String   @default("IDR")
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@unique([date, courseId, tutorId])
  @@map("revenue_analytics")
}

model SystemHealth {
  id                String   @id @default(cuid())
  timestamp         DateTime @default(now())
  authServiceStatus String   @default("unknown") @map("auth_service_status")
  courseServiceStatus String @default("unknown") @map("course_service_status")
  paymentServiceStatus String @default("unknown") @map("payment_service_status")
  analyticsServiceStatus String @default("unknown") @map("analytics_service_status")
  databaseConnections Int    @default(0) @map("database_connections")
  memoryUsage       Json?    @map("memory_usage")
  responseTime      Int?     @map("response_time") // in milliseconds
  errorRate         Decimal  @default(0) @db.Decimal(5, 2) @map("error_rate")

  @@map("system_health")
}