import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import cron from 'node-cron';

import { errorHandler } from './middleware/errorHandler';
import { authenticateToken } from './middleware/auth';
import paymentRoutes from './routes/payments';
import subscriptionRoutes from './routes/subscriptions';
import webhookRoutes from './routes/webhooks';
import { checkExpiredInvoices } from './services/cronJobs';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3003;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200,
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://timeacademy.com']
    : true,
  credentials: true,
}));

// Webhook routes need raw body, so they come before JSON parsing
app.use('/api/webhooks', webhookRoutes);

app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Payment Service is healthy',
    timestamp: new Date().toISOString(),
    service: 'payment-service',
    version: '1.0.0'
  });
});

// API Routes
app.use('/api/payments', authenticateToken, paymentRoutes);
app.use('/api/subscriptions', authenticateToken, subscriptionRoutes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

// Cron jobs
if (process.env.NODE_ENV === 'production') {
  // Check for expired invoices every hour
  cron.schedule('0 * * * *', checkExpiredInvoices);
}

app.listen(PORT, () => {
  console.log(`🚀 Payment Service running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
});