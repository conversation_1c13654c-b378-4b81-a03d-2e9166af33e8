import { Router, Response, NextFunction } from "express";
import { body, query, param, validationResult } from "express-validator";
import { PrismaClient } from "@prisma/client";
import {
  authenticateToken,
  requireRole,
  AuthenticatedRequest,
} from "../shared/middleware/auth";
import { UserRole } from "../shared/types";
import { AppError } from "../middleware/errorHandler";

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: "Validation failed",
      details: errors.array(),
    });
  }
  next();
};

// GET /api/users - Get all users (Admin only)
router.get(
  "/",
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  [
    query("role").optional().isIn(["STUDENT", "TUTOR", "ADMIN"]),
    query("isActive").optional().isBoolean(),
    query("page").optional().isInt({ min: 1 }).toInt(),
    query("limit").optional().isInt({ min: 1, max: 100 }).toInt(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { role, isActive, page = 1, limit = 20 } = req.query;
      const pageNum = parseInt(page as string) || 1;
      const limitNum = parseInt(limit as string) || 20;
      const offset = (pageNum - 1) * limitNum;

      const where: any = {};
      if (role) where.role = role;
      if (isActive !== undefined) where.isActive = isActive === "true";

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          include: {
            tutorProfile: true,
          },
          orderBy: { createdAt: "desc" },
          take: limitNum,
          skip: offset,
        }),
        prisma.user.count({ where }),
      ]);

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            total,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(total / limitNum),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/users/:id - Get user by ID
router.get(
  "/:id",
  authenticateToken,
  [param("id").isString().notEmpty()],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      // Users can only view their own profile unless they're admin
      if (req.user!.role !== UserRole.ADMIN && req.user!.id !== id) {
        throw new AppError("Access denied", 403);
      }

      const user = await prisma.user.findUnique({
        where: { id },
        include: {
          tutorProfile: true,
        },
      });

      if (!user) {
        throw new AppError("User not found", 404);
      }

      res.json({
        success: true,
        data: { user },
      });
    } catch (error) {
      next(error);
    }
  }
);

// PUT /api/users/:id - Update user
router.put(
  "/:id",
  authenticateToken,
  [
    param("id").isString().notEmpty(),
    body("displayName").optional().isLength({ min: 2, max: 100 }),
    body("photoURL").optional().isURL(),
    body("isActive").optional().isBoolean(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { displayName, photoURL, isActive } = req.body;

      // Users can only update their own profile unless they're admin
      if (req.user!.role !== UserRole.ADMIN && req.user!.id !== id) {
        throw new AppError("Access denied", 403);
      }

      // Only admins can change isActive status
      const updateData: any = {};
      if (displayName) updateData.displayName = displayName;
      if (photoURL) updateData.photoURL = photoURL;
      if (isActive !== undefined && req.user!.role === UserRole.ADMIN) {
        updateData.isActive = isActive;
      }

      const user = await prisma.user.update({
        where: { id },
        data: updateData,
        include: {
          tutorProfile: true,
        },
      });

      res.json({
        success: true,
        data: { user },
        message: "User updated successfully",
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/users/tutor-application - Apply to become tutor
router.post(
  "/tutor-application",
  authenticateToken,
  requireRole([UserRole.STUDENT]),
  [
    body("bio").notEmpty().isLength({ min: 50, max: 1000 }),
    body("expertise").isArray({ min: 1 }),
    body("expertise.*").isString().isLength({ min: 2, max: 50 }),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { bio, expertise } = req.body;

      // Check if user already has a tutor profile
      const existingProfile = await prisma.tutorProfile.findUnique({
        where: { userId: req.user!.id },
      });

      if (existingProfile) {
        throw new AppError("Tutor application already exists", 400);
      }

      const tutorProfile = await prisma.tutorProfile.create({
        data: {
          userId: req.user!.id,
          bio,
          expertise,
        },
      });

      res.status(201).json({
        success: true,
        data: { tutorProfile },
        message:
          "Tutor application submitted successfully. Awaiting admin approval.",
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
