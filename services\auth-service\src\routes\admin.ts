import { Router, Response, NextFunction } from "express";
import { body, query, param, validationResult } from "express-validator";
import { PrismaClient } from "@prisma/client";
import admin from "firebase-admin";
import {
  authenticateToken,
  requireRole,
  AuthenticatedRequest,
} from "../shared/middleware/auth";
import { UserRole } from "../shared/types";
import { AppError } from "../middleware/errorHandler";

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({
      success: false,
      error: "Validation failed",
      details: errors.array(),
    });
    return;
  }
  next();
};

// GET /api/admin/tutor-applications - Get pending tutor applications
router.get(
  "/tutor-applications",
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  [
    query("status").optional().isIn(["pending", "approved", "rejected"]),
    query("page").optional().isInt({ min: 1 }).toInt(),
    query("limit").optional().isInt({ min: 1, max: 50 }).toInt(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { status = "pending", page = 1, limit = 20 } = req.query;
      const pageNum = parseInt(page as string) || 1;
      const limitNum = parseInt(limit as string) || 20;
      const offset = (pageNum - 1) * limitNum;

      const where: any = {};
      if (status === "pending") where.isApproved = false;
      if (status === "approved") where.isApproved = true;

      const [applications, total] = await Promise.all([
        prisma.tutorProfile.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                displayName: true,
                photoURL: true,
                createdAt: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
          take: limitNum,
          skip: offset,
        }),
        prisma.tutorProfile.count({ where }),
      ]);

      res.json({
        success: true,
        data: {
          applications,
          pagination: {
            total,
            page: pageNum,
            limit: limitNum,
            totalPages: Math.ceil(total / limitNum),
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/admin/tutors/:id/approve - Approve tutor application
router.post(
  "/tutors/:id/approve",
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  [param("id").isString().notEmpty()],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;

      const tutorProfile = await prisma.tutorProfile.findUnique({
        where: { id },
        include: { user: true },
      });

      if (!tutorProfile) {
        throw new AppError("Tutor application not found", 404);
      }

      if (tutorProfile.isApproved) {
        throw new AppError("Tutor already approved", 400);
      }

      // Update tutor profile and user role in transaction
      await prisma.$transaction(async (tx) => {
        await tx.tutorProfile.update({
          where: { id },
          data: {
            isApproved: true,
            approvedBy: req.user!.id,
            approvedAt: new Date(),
          },
        });

        await tx.user.update({
          where: { id: tutorProfile.userId },
          data: { role: UserRole.TUTOR },
        });

        await tx.adminAction.create({
          data: {
            adminId: req.user!.id,
            action: "TUTOR_APPROVED",
            targetId: tutorProfile.userId,
            targetType: "USER",
            details: {
              tutorProfileId: id,
              email: tutorProfile.user.email,
            },
          },
        });
      });

      res.json({
        success: true,
        message: "Tutor approved successfully",
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/admin/tutors/:id/reject - Reject tutor application
router.post(
  "/tutors/:id/reject",
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  [
    param("id").isString().notEmpty(),
    body("reason").optional().isString().isLength({ max: 500 }),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const tutorProfile = await prisma.tutorProfile.findUnique({
        where: { id },
        include: { user: true },
      });

      if (!tutorProfile) {
        throw new AppError("Tutor application not found", 404);
      }

      await prisma.$transaction(async (tx) => {
        await tx.tutorProfile.delete({
          where: { id },
        });

        await tx.adminAction.create({
          data: {
            adminId: req.user!.id,
            action: "TUTOR_REJECTED",
            targetId: tutorProfile.userId,
            targetType: "USER",
            details: {
              email: tutorProfile.user.email,
              reason: reason || "No reason provided",
            },
          },
        });
      });

      res.json({
        success: true,
        message: "Tutor application rejected",
      });
    } catch (error) {
      next(error);
    }
  }
);

// POST /api/admin/users - Create user (Admin only)
router.post(
  "/users",
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  [
    body("email").isEmail(),
    body("displayName").notEmpty().isLength({ min: 2, max: 100 }),
    body("role").isIn(["STUDENT", "TUTOR", "ADMIN"]),
    body("password").isLength({ min: 6 }),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { email, displayName, role, password } = req.body;

      // Create user in Firebase first
      const firebaseUser = await admin.auth().createUser({
        email,
        displayName,
        password,
      });

      // Create user in our database
      const user = await prisma.user.create({
        data: {
          firebaseUid: firebaseUser.uid,
          email,
          displayName,
          role,
        },
      });

      // Log admin action
      await prisma.adminAction.create({
        data: {
          adminId: req.user!.id,
          action: "USER_CREATED",
          targetId: user.id,
          targetType: "USER",
          details: {
            email,
            role,
          },
        },
      });

      res.status(201).json({
        success: true,
        data: { user },
        message: "User created successfully",
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/admin/analytics - Get admin dashboard analytics
router.get(
  "/analytics",
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  async (_req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const [totalUsers, totalTutors, pendingApplications, recentActions] =
        await Promise.all([
          prisma.user.count(),
          prisma.user.count({ where: { role: UserRole.TUTOR } }),
          prisma.tutorProfile.count({ where: { isApproved: false } }),
          prisma.adminAction.findMany({
            take: 10,
            orderBy: { createdAt: "desc" },
          }),
        ]);

      res.json({
        success: true,
        data: {
          totalUsers,
          totalTutors,
          pendingApplications,
          recentActions,
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
