import { Router } from 'express';
import { body, param, validationResult } from 'express-validator';
import multer from 'multer';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { AppError } from '../middleware/errorHandler';
import bunnyService from '../services/bunnyService';

const router = Router();
const prisma = new PrismaClient();

// Configure multer for video uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 500 * 1024 * 1024, // 500MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedMimes = ['video/mp4', 'video/avi', 'video/mov', 'video/wmv'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only video files are allowed.'));
    }
  },
});

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/videos/upload - Upload video to Bunny.net
router.post('/upload', 
  authenticateToken, 
  requireRole([UserRole.TUTOR, UserRole.ADMIN]),
  upload.single('video'),
  [
    body('title').notEmpty().isLength({ min: 3, max: 200 }),
    body('description').optional().isLength({ max: 1000 }),
    body('lessonId').isString().notEmpty(),
  ], 
  handleValidationErrors, 
  async (req: AuthenticatedRequest, res, next) => {
    try {
      if (!req.file) {
        throw new AppError('Video file is required', 400);
      }

      const { title, description, lessonId } = req.body;

      // Verify lesson exists and user has permission
      const lesson = await prisma.lesson.findUnique({
        where: { id: lessonId },
        include: {
          module: {
            include: { course: true }
          }
        }
      });

      if (!lesson) {
        throw new AppError('Lesson not found', 404);
      }

      // Check if user owns the course (for tutors)
      if (req.user!.role === UserRole.TUTOR && lesson.module.course.tutorId !== req.user!.id) {
        throw new AppError('You can only upload videos to your own courses', 403);
      }

      // Upload to Bunny.net
      const uploadResult = await bunnyService.uploadVideo(
        req.file.buffer,
        title,
        description
      );

      // Update lesson with video details
      const updatedLesson = await prisma.lesson.update({
        where: { id: lessonId },
        data: {
          bunnyVideoId: uploadResult.guid,
          videoUrl: uploadResult.frameworkUrl,
          videoDuration: uploadResult.length,
        }
      });

      res.json({
        success: true,
        data: {
          lesson: updatedLesson,
          bunnyVideo: uploadResult,
        },
        message: 'Video uploaded successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/videos/:videoId/stream - Get secure streaming URL
router.get('/:videoId/stream', 
  authenticateToken,
  [
    param('videoId').isString().notEmpty(),
  ], 
  handleValidationErrors, 
  async (req: AuthenticatedRequest, res, next) => {
    try {
      const { videoId } = req.params;

      // Find lesson with this video
      const lesson = await prisma.lesson.findFirst({
        where: { bunnyVideoId: videoId },
        include: {
          module: {
            include: { course: true }
          }
        }
      });

      if (!lesson) {
        throw new AppError('Video not found', 404);
      }

      // Check if user has access to this course
      const subscription = await prisma.subscription.findUnique({
        where: {
          studentId_courseId: {
            studentId: req.user!.id,
            courseId: lesson.module.courseId,
          }
        }
      });

      if (!subscription || subscription.status !== 'ACTIVE') {
        throw new AppError('Access denied. Active subscription required.', 403);
      }

      // Generate secure streaming URL (expires in 2 hours)
      const expirationTime = Math.floor(Date.now() / 1000) + 7200;
      const streamUrl = bunnyService.generateSecureStreamUrl(videoId, expirationTime);

      // Get video details
      const videoDetails = await bunnyService.getVideoDetails(videoId);

      res.json({
        success: true,
        data: {
          streamUrl,
          videoDetails,
          lesson: {
            id: lesson.id,
            title: lesson.title,
            description: lesson.description,
            videoDuration: lesson.videoDuration,
          }
        }
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/videos/:videoId/analytics - Get video analytics
router.get('/:videoId/analytics', 
  authenticateToken,
  requireRole([UserRole.TUTOR, UserRole.ADMIN]),
  [
    param('videoId').isString().notEmpty(),
  ], 
  handleValidationErrors, 
  async (req: AuthenticatedRequest, res, next) => {
    try {
      const { videoId } = req.params;
      const { dateFrom, dateTo } = req.query;

      // Find lesson with this video
      const lesson = await prisma.lesson.findFirst({
        where: { bunnyVideoId: videoId },
        include: {
          module: {
            include: { course: true }
          }
        }
      });

      if (!lesson) {
        throw new AppError('Video not found', 404);
      }

      // Check if user owns the course (for tutors)
      if (req.user!.role === UserRole.TUTOR && lesson.module.course.tutorId !== req.user!.id) {
        throw new AppError('You can only view analytics for your own videos', 403);
      }

      const analytics = await bunnyService.getVideoAnalytics(
        videoId,
        dateFrom as string,
        dateTo as string
      );

      res.json({
        success: true,
        data: { analytics }
      });
    } catch (error) {
      next(error);
    }
  }
);

// DELETE /api/videos/:videoId - Delete video
router.delete('/:videoId', 
  authenticateToken,
  requireRole([UserRole.TUTOR, UserRole.ADMIN]),
  [
    param('videoId').isString().notEmpty(),
  ], 
  handleValidationErrors, 
  async (req: AuthenticatedRequest, res, next) => {
    try {
      const { videoId } = req.params;

      // Find lesson with this video
      const lesson = await prisma.lesson.findFirst({
        where: { bunnyVideoId: videoId },
        include: {
          module: {
            include: { course: true }
          }
        }
      });

      if (!lesson) {
        throw new AppError('Video not found', 404);
      }

      // Check if user owns the course (for tutors)
      if (req.user!.role === UserRole.TUTOR && lesson.module.course.tutorId !== req.user!.id) {
        throw new AppError('You can only delete your own videos', 403);
      }

      // Delete from Bunny.net
      await bunnyService.deleteVideo(videoId);

      // Update lesson to remove video references
      await prisma.lesson.update({
        where: { id: lesson.id },
        data: {
          bunnyVideoId: null,
          videoUrl: null,
          videoDuration: null,
        }
      });

      res.json({
        success: true,
        message: 'Video deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;