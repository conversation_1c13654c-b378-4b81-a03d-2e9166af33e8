'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import { 
  BookOpen, 
  Clock, 
  Users, 
  Star,
  Play,
  CheckCircle,
  Lock,
  Award,
  Calendar,
  CreditCard
} from 'lucide-react';
import toast from 'react-hot-toast';

interface CourseDetails {
  id: string;
  title: string;
  description: string;
  category: string;
  durationDays: number;
  price: number;
  currency: string;
  thumbnailUrl?: string;
  hasAccess: boolean;
  modules: Array<{
    id: string;
    title: string;
    description: string;
    orderIndex: number;
    lessons: Array<{
      id: string;
      title: string;
      description: string;
      orderIndex: number;
      videoDuration?: number;
      assignments: Array<{
        id: string;
        title: string;
        type: string;
        videoTimestamp?: number;
        timeLimit: number;
        passingScore: number;
      }>;
    }>;
  }>;
  _count: {
    subscriptions: number;
  };
}

export default function CourseDetailPage() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const courseId = params.id as string;
  
  const [course, setCourse] = useState<CourseDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPurchasing, setIsPurchasing] = useState(false);

  useEffect(() => {
    if (courseId) {
      fetchCourseDetails();
    }
  }, [courseId]);

  const fetchCourseDetails = async () => {
    try {
      const response = await api.get(endpoints.courses.get(courseId));
      if (response.data.success) {
        setCourse(response.data.data.course);
      }
    } catch (error) {
      console.error('Error fetching course details:', error);
      toast.error('Failed to load course details');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePurchase = async () => {
    if (!user) {
      router.push('/login');
      return;
    }

    try {
      setIsPurchasing(true);
      const response = await api.post(endpoints.payments.createInvoice, {
        courseId,
        amount: course!.price,
        currency: course!.currency,
      });

      if (response.data.success) {
        // Redirect to payment page
        window.location.href = response.data.data.invoiceUrl;
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to create payment');
    } finally {
      setIsPurchasing(false);
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDuration = (days: number) => {
    if (days < 30) {
      return `${days} days`;
    } else if (days < 365) {
      const months = Math.floor(days / 30);
      return `${months} month${months > 1 ? 's' : ''}`;
    } else {
      const years = Math.floor(days / 365);
      return `${years} year${years > 1 ? 's' : ''}`;
    }
  };

  const getTotalLessons = () => {
    return course?.modules.reduce((total, module) => total + module.lessons.length, 0) || 0;
  };

  const getTotalAssignments = () => {
    return course?.modules.reduce((total, module) => 
      total + module.lessons.reduce((lessonTotal, lesson) => 
        lessonTotal + lesson.assignments.length, 0
      ), 0
    ) || 0;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner w-8 h-8"></div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Course not found</h1>
          <p className="text-gray-600 mb-4">The course you're looking for doesn't exist.</p>
          <button onClick={() => router.push('/courses')} className="btn-primary">
            Browse Courses
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Course Info */}
            <div>
              <div className="flex items-center mb-4">
                <span className="badge badge-primary mr-3">{course.category}</span>
                <div className="flex items-center text-yellow-400">
                  <Star className="h-5 w-5 fill-current" />
                  <span className="text-sm text-gray-600 ml-1">4.8 (124 reviews)</span>
                </div>
              </div>

              <h1 className="text-4xl font-bold text-gray-900 mb-4">
                {course.title}
              </h1>

              <p className="text-lg text-gray-600 mb-6">
                {course.description}
              </p>

              {/* Course Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div className="text-center">
                  <Clock className="h-6 w-6 text-primary-600 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Duration</p>
                  <p className="font-semibold">{formatDuration(course.durationDays)}</p>
                </div>
                <div className="text-center">
                  <BookOpen className="h-6 w-6 text-primary-600 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Lessons</p>
                  <p className="font-semibold">{getTotalLessons()}</p>
                </div>
                <div className="text-center">
                  <CheckCircle className="h-6 w-6 text-primary-600 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Assignments</p>
                  <p className="font-semibold">{getTotalAssignments()}</p>
                </div>
                <div className="text-center">
                  <Users className="h-6 w-6 text-primary-600 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">Students</p>
                  <p className="font-semibold">{course._count.subscriptions}</p>
                </div>
              </div>

              {/* Price and Purchase */}
              <div className="bg-gray-50 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <p className="text-3xl font-bold text-gray-900">
                      {formatPrice(course.price, course.currency)}
                    </p>
                    <p className="text-sm text-gray-500">One-time payment</p>
                  </div>
                  <Award className="h-8 w-8 text-primary-600" />
                </div>

                {course.hasAccess ? (
                  <button
                    onClick={() => router.push(`/student/courses/${course.id}`)}
                    className="btn-primary w-full flex items-center justify-center"
                  >
                    <Play className="h-5 w-5 mr-2" />
                    Continue Learning
                  </button>
                ) : (
                  <button
                    onClick={handlePurchase}
                    disabled={isPurchasing}
                    className="btn-primary w-full flex items-center justify-center"
                  >
                    {isPurchasing ? (
                      <div className="spinner w-5 h-5 mr-2"></div>
                    ) : (
                      <CreditCard className="h-5 w-5 mr-2" />
                    )}
                    {isPurchasing ? 'Processing...' : 'Enroll Now'}
                  </button>
                )}

                <div className="mt-4 text-center">
                  <p className="text-xs text-gray-500">
                    30-day money-back guarantee • Lifetime access
                  </p>
                </div>
              </div>
            </div>

            {/* Course Preview */}
            <div>
              <div className="bg-gray-900 rounded-lg aspect-video flex items-center justify-center">
                {course.thumbnailUrl ? (
                  <img
                    src={course.thumbnailUrl}
                    alt={course.title}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <div className="text-center text-white">
                    <Play className="h-16 w-16 mx-auto mb-4 opacity-75" />
                    <p className="text-lg">Course Preview</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Course Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Course Content</h2>
            
            <div className="space-y-4">
              {course.modules.map((module, moduleIndex) => (
                <div key={module.id} className="card">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      Module {moduleIndex + 1}: {module.title}
                    </h3>
                    <span className="text-sm text-gray-500">
                      {module.lessons.length} lessons
                    </span>
                  </div>

                  {module.description && (
                    <p className="text-gray-600 mb-4">{module.description}</p>
                  )}

                  <div className="space-y-2">
                    {module.lessons.map((lesson, lessonIndex) => (
                      <div key={lesson.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                        <div className="flex items-center">
                          {course.hasAccess ? (
                            <CheckCircle className="h-5 w-5 text-success-600 mr-3" />
                          ) : (
                            <Lock className="h-5 w-5 text-gray-400 mr-3" />
                          )}
                          <div>
                            <p className="font-medium text-gray-900">
                              {lessonIndex + 1}. {lesson.title}
                            </p>
                            {lesson.description && (
                              <p className="text-sm text-gray-500">{lesson.description}</p>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center text-sm text-gray-500">
                          {lesson.videoDuration && (
                            <span className="mr-4">
                              {Math.floor(lesson.videoDuration / 60)}:{(lesson.videoDuration % 60).toString().padStart(2, '0')}
                            </span>
                          )}
                          {lesson.assignments.length > 0 && (
                            <span className="badge badge-gray text-xs">
                              {lesson.assignments.length} quiz{lesson.assignments.length > 1 ? 'zes' : ''}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="card sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                What you'll learn
              </h3>
              <ul className="space-y-3">
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Master {course.category} test strategies</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Improve your English proficiency</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Practice with real exam questions</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Get personalized feedback</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-success-600 mr-3 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-700">Earn a verified certificate</span>
                </li>
              </ul>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-3">Course Features</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    {formatDuration(course.durationDays)} access
                  </li>
                  <li className="flex items-center">
                    <Play className="h-4 w-4 mr-2" />
                    HD video content
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Interactive assignments
                  </li>
                  <li className="flex items-center">
                    <Award className="h-4 w-4 mr-2" />
                    Certificate of completion
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}