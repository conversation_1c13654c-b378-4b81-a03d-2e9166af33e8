import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import cron from 'node-cron';

import { errorHandler } from './middleware/errorHandler';
import { authenticateToken } from './middleware/auth';
import analyticsRoutes from './routes/analytics';
import reportsRoutes from './routes/reports';
import { aggregateDailyMetrics, aggregateCourseAnalytics, checkSystemHealth } from './services/cronJobs';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3004;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100,
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://timeacademy.com']
    : true,
  credentials: true,
}));
app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Analytics Service is healthy',
    timestamp: new Date().toISOString(),
    service: 'analytics-service',
    version: '1.0.0'
  });
});

// API Routes
app.use('/api/analytics', authenticateToken, analyticsRoutes);
app.use('/api/reports', authenticateToken, reportsRoutes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

// Cron jobs
if (process.env.NODE_ENV === 'production') {
  // Aggregate daily metrics at midnight
  cron.schedule('0 0 * * *', aggregateDailyMetrics);
  
  // Aggregate course analytics at 1 AM
  cron.schedule('0 1 * * *', aggregateCourseAnalytics);
  
  // Check system health every 5 minutes
  cron.schedule('*/5 * * * *', checkSystemHealth);
}

app.listen(PORT, () => {
  console.log(`🚀 Analytics Service running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
});