import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import xenditService from '../services/xenditService';
import axios from 'axios';

const router = Router();
const prisma = new PrismaClient();

// Middleware to parse raw body for webhook signature verification
router.use('/xendit', (req, res, next) => {
  let rawBody = '';
  req.on('data', chunk => {
    rawBody += chunk.toString();
  });
  req.on('end', () => {
    (req as any).rawBody = rawBody;
    try {
      req.body = JSON.parse(rawBody);
    } catch (error) {
      req.body = {};
    }
    next();
  });
});

// POST /api/webhooks/xendit - Handle Xendit webhook
router.post('/xendit', async (req, res) => {
  try {
    const signature = req.headers['x-callback-token'] as string;
    const rawBody = (req as any).rawBody;

    // Log webhook for debugging
    await prisma.webhookLog.create({
      data: {
        provider: 'xendit',
        eventType: req.body.status || 'unknown',
        eventId: req.body.id,
        payload: req.body,
      }
    });

    // Verify webhook signature
    if (!xenditService.verifyWebhookSignature(rawBody, signature)) {
      console.error('Invalid webhook signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    const webhookData = xenditService.processWebhook(req.body);
    
    // Find payment intent
    const paymentIntent = await prisma.paymentIntent.findUnique({
      where: { xenditInvoiceId: webhookData.invoiceId }
    });

    if (!paymentIntent) {
      console.error('Payment intent not found for invoice:', webhookData.invoiceId);
      return res.status(404).json({ error: 'Payment intent not found' });
    }

    // Process based on status
    switch (webhookData.status) {
      case 'PAID':
      case 'SETTLED':
        await handleSuccessfulPayment(paymentIntent, webhookData);
        break;
      
      case 'EXPIRED':
        await handleExpiredPayment(paymentIntent);
        break;
      
      case 'FAILED':
        await handleFailedPayment(paymentIntent, webhookData);
        break;
      
      default:
        console.log('Unhandled webhook status:', webhookData.status);
    }

    // Update webhook log as processed
    await prisma.webhookLog.updateMany({
      where: { 
        eventId: webhookData.invoiceId,
        processed: false 
      },
      data: { processed: true }
    });

    res.json({ success: true });
  } catch (error) {
    console.error('Webhook processing error:', error);
    
    // Update webhook log with error
    if (req.body.id) {
      await prisma.webhookLog.updateMany({
        where: { 
          eventId: req.body.id,
          processed: false 
        },
        data: { 
          processed: true,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }

    res.status(500).json({ error: 'Webhook processing failed' });
  }
});

// Handle successful payment
async function handleSuccessfulPayment(paymentIntent: any, webhookData: any) {
  try {
    await prisma.$transaction(async (tx) => {
      // Update payment intent
      await tx.paymentIntent.update({
        where: { id: paymentIntent.id },
        data: {
          status: 'COMPLETED',
          xenditPaymentId: webhookData.invoiceId,
          paidAt: webhookData.paidAt,
        }
      });

      // Get course details to calculate subscription dates
      const courseResponse = await axios.get(
        `${process.env.COURSE_SERVICE_URL}/api/courses/${paymentIntent.courseId}`
      );

      if (!courseResponse.data.success) {
        throw new Error('Course not found');
      }

      const course = courseResponse.data.data.course;
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(startDate.getDate() + course.durationDays);

      // Create or update subscription
      await tx.subscription.upsert({
        where: {
          studentId_courseId: {
            studentId: paymentIntent.studentId,
            courseId: paymentIntent.courseId,
          }
        },
        create: {
          studentId: paymentIntent.studentId,
          courseId: paymentIntent.courseId,
          paymentId: paymentIntent.id,
          status: 'ACTIVE',
          startDate,
          endDate,
        },
        update: {
          paymentId: paymentIntent.id,
          status: 'ACTIVE',
          startDate,
          endDate,
          cancelledAt: null,
          cancelReason: null,
        }
      });
    });

    console.log(`Successfully processed payment for student ${paymentIntent.studentId}, course ${paymentIntent.courseId}`);
  } catch (error) {
    console.error('Error handling successful payment:', error);
    throw error;
  }
}

// Handle expired payment
async function handleExpiredPayment(paymentIntent: any) {
  await prisma.paymentIntent.update({
    where: { id: paymentIntent.id },
    data: {
      status: 'EXPIRED',
      failureReason: 'Payment expired',
    }
  });

  console.log(`Payment expired for student ${paymentIntent.studentId}, course ${paymentIntent.courseId}`);
}

// Handle failed payment
async function handleFailedPayment(paymentIntent: any, webhookData: any) {
  await prisma.paymentIntent.update({
    where: { id: paymentIntent.id },
    data: {
      status: 'FAILED',
      failureReason: 'Payment failed',
    }
  });

  console.log(`Payment failed for student ${paymentIntent.studentId}, course ${paymentIntent.courseId}`);
}

export default router;