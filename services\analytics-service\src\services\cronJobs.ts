import { PrismaClient } from "@prisma/client";
import axios from "axios";

interface CourseAnalyticsData {
  id: string;
  enrollments: number;
  activeStudents: number;
  completedStudents: number;
  terminatedStudents: number;
  averageProgress: number;
  totalWatchTime: number;
  assignmentSubmissions: number;
  assignmentPassRate: number;
  revenue: number;
}

const prisma = new PrismaClient();

// Aggregate daily metrics from all services
export async function aggregateDailyMetrics() {
  try {
    console.log("Aggregating daily metrics...");

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get data from all services
    const [authData, courseData, paymentData] = await Promise.all([
      fetchAuthServiceData(),
      fetchCourseServiceData(),
      fetchPaymentServiceData(),
    ]);

    // Aggregate the data
    const metrics = {
      date: today,
      totalUsers: authData.totalUsers,
      newUsers: authData.newUsers,
      activeSubscriptions: paymentData.activeSubscriptions,
      newSubscriptions: paymentData.newSubscriptions,
      cancelledSubscriptions: paymentData.cancelledSubscriptions,
      terminatedSubscriptions: paymentData.terminatedSubscriptions,
      totalRevenue: paymentData.totalRevenue,
      newRevenue: paymentData.newRevenue,
      totalCourses: courseData.totalCourses,
      publishedCourses: courseData.publishedCourses,
      totalLessons: courseData.totalLessons,
      totalAssignments: courseData.totalAssignments,
      assignmentSubmissions: courseData.assignmentSubmissions,
      passedAssignments: courseData.passedAssignments,
      failedAssignments: courseData.failedAssignments,
    };

    // Upsert daily metrics
    await prisma.dailyMetrics.upsert({
      where: { date: today },
      create: metrics,
      update: metrics,
    });

    console.log("Daily metrics aggregated successfully");
  } catch (error) {
    console.error("Error aggregating daily metrics:", error);
  }
}

// Aggregate course analytics
export async function aggregateCourseAnalytics() {
  try {
    console.log("Aggregating course analytics...");

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get course data from course service
    const courseData = await fetchCourseAnalyticsData();

    // Process each course
    for (const course of courseData.courses) {
      const analytics = {
        courseId: course.id,
        date: today,
        enrollments: course.enrollments || 0,
        activeStudents: course.activeStudents || 0,
        completedStudents: course.completedStudents || 0,
        terminatedStudents: course.terminatedStudents || 0,
        averageProgress: course.averageProgress || 0,
        totalWatchTime: course.totalWatchTime || 0,
        assignmentSubmissions: course.assignmentSubmissions || 0,
        assignmentPassRate: course.assignmentPassRate || 0,
        revenue: course.revenue || 0,
      };

      await prisma.courseAnalytics.upsert({
        where: {
          courseId_date: {
            courseId: course.id,
            date: today,
          },
        },
        create: analytics,
        update: analytics,
      });
    }

    console.log(
      `Course analytics aggregated for ${courseData.courses.length} courses`
    );
  } catch (error) {
    console.error("Error aggregating course analytics:", error);
  }
}

// Check system health
export async function checkSystemHealth() {
  try {
    const healthData = {
      timestamp: new Date(),
      authServiceStatus: "unknown",
      courseServiceStatus: "unknown",
      paymentServiceStatus: "unknown",
      analyticsServiceStatus: "healthy", // Self
      databaseConnections: 0,
      memoryUsage: process.memoryUsage() as any,
      responseTime: 0,
      errorRate: 0,
    };

    // Check each service
    const services = [
      {
        name: "auth",
        url: process.env.AUTH_SERVICE_URL || "http://auth-service:3001",
        field: "authServiceStatus",
      },
      {
        name: "course",
        url: process.env.COURSE_SERVICE_URL || "http://course-service:3002",
        field: "courseServiceStatus",
      },
      {
        name: "payment",
        url: process.env.PAYMENT_SERVICE_URL || "http://payment-service:3003",
        field: "paymentServiceStatus",
      },
    ];

    for (const service of services) {
      try {
        const startTime = Date.now();
        const response = await axios.get(`${service.url}/health`, {
          timeout: 5000,
        });
        const responseTime = Date.now() - startTime;

        if (response.status === 200) {
          (healthData as any)[service.field] = "healthy";
          if (healthData.responseTime === 0)
            healthData.responseTime = Math.round(responseTime);
        } else {
          (healthData as any)[service.field] = "unhealthy";
        }
      } catch (error) {
        (healthData as any)[service.field] = "down";
      }
    }

    // Save health data
    await prisma.systemHealth.create({
      data: healthData,
    });

    // Clean up old health records (keep last 1000)
    const oldRecords = await prisma.systemHealth.findMany({
      orderBy: { timestamp: "desc" },
      skip: 1000,
      select: { id: true },
    });

    if (oldRecords.length > 0) {
      await prisma.systemHealth.deleteMany({
        where: {
          id: { in: oldRecords.map((r: any) => r.id) },
        },
      });
    }
  } catch (error) {
    console.error("Error checking system health:", error);
  }
}

// Helper functions to fetch data from other services
async function fetchAuthServiceData() {
  try {
    const response = await axios.get(
      `${process.env.AUTH_SERVICE_URL}/api/admin/analytics`,
      {
        timeout: 10000,
      }
    );
    return response.data.data;
  } catch (error) {
    console.error("Error fetching auth service data:", error);
    return {
      totalUsers: 0,
      newUsers: 0,
    };
  }
}

async function fetchCourseServiceData() {
  try {
    // This would be an endpoint that provides course statistics
    // For now, return mock data
    return {
      totalCourses: 0,
      publishedCourses: 0,
      totalLessons: 0,
      totalAssignments: 0,
      assignmentSubmissions: 0,
      passedAssignments: 0,
      failedAssignments: 0,
    };
  } catch (error) {
    console.error("Error fetching course service data:", error);
    return {
      totalCourses: 0,
      publishedCourses: 0,
      totalLessons: 0,
      totalAssignments: 0,
      assignmentSubmissions: 0,
      passedAssignments: 0,
      failedAssignments: 0,
    };
  }
}

async function fetchPaymentServiceData() {
  try {
    // This would be an endpoint that provides payment statistics
    // For now, return mock data
    return {
      activeSubscriptions: 0,
      newSubscriptions: 0,
      cancelledSubscriptions: 0,
      terminatedSubscriptions: 0,
      totalRevenue: 0,
      newRevenue: 0,
    };
  } catch (error) {
    console.error("Error fetching payment service data:", error);
    return {
      activeSubscriptions: 0,
      newSubscriptions: 0,
      cancelledSubscriptions: 0,
      terminatedSubscriptions: 0,
      totalRevenue: 0,
      newRevenue: 0,
    };
  }
}

async function fetchCourseAnalyticsData(): Promise<{
  courses: CourseAnalyticsData[];
}> {
  try {
    // This would fetch detailed course analytics from course service
    // For now, return mock data
    return {
      courses: [] as CourseAnalyticsData[],
    };
  } catch (error) {
    console.error("Error fetching course analytics data:", error);
    return {
      courses: [] as CourseAnalyticsData[],
    };
  }
}
