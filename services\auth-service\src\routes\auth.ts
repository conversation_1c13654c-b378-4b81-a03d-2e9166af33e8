import { Router, Request, Response, NextFunction } from "express";
import { body, validationResult } from "express-validator";
import admin from "firebase-admin";
import { PrismaClient } from "@prisma/client";
import { AppError } from "../middleware/errorHandler";

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const validateFirebaseToken = [
  body("idToken").notEmpty().withMessage("Firebase ID token is required"),
];

const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: "Validation failed",
      details: errors.array(),
    });
  }
  next();
};

// POST /api/auth/verify-token
router.post(
  "/verify-token",
  validateFirebaseToken,
  handleValidationErrors,
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { idToken } = req.body;

      // Verify Firebase ID token
      const decodedToken = await admin.auth().verifyIdToken(idToken);
      const { uid, email, name, picture } = decodedToken;

      // Check if user exists in our database
      let user = await prisma.user.findUnique({
        where: { firebaseUid: uid },
        include: { tutorProfile: true },
      });

      // If user doesn't exist, create them
      if (!user) {
        user = await prisma.user.create({
          data: {
            firebaseUid: uid,
            email: email!,
            displayName: name || email?.split("@")[0] || "User",
            photoURL: picture,
            role: "STUDENT", // Default role
          },
          include: { tutorProfile: true },
        });
      }

      // Create or update user session
      await prisma.userSession.upsert({
        where: {
          userId_firebaseUid: {
            userId: user.id,
            firebaseUid: uid,
          },
        },
        create: {
          userId: user.id,
          firebaseUid: uid,
          deviceInfo: req.get("User-Agent"),
          ipAddress: req.ip,
        },
        update: {
          lastActivity: new Date(),
          deviceInfo: req.get("User-Agent"),
          ipAddress: req.ip,
        },
      });

      res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            firebaseUid: user.firebaseUid,
            email: user.email,
            displayName: user.displayName,
            photoURL: user.photoURL,
            role: user.role,
            isActive: user.isActive,
            tutorProfile: user.tutorProfile,
          },
        },
      });
    } catch (error) {
      console.error("Token verification error:", error);
      next(new AppError("Invalid token", 401));
    }
  }
);

// POST /api/auth/logout
router.post(
  "/logout",
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(" ")[1];

      if (token) {
        try {
          const decodedToken = await admin.auth().verifyIdToken(token);

          // Remove user session
          await prisma.userSession.deleteMany({
            where: { firebaseUid: decodedToken.uid },
          });
        } catch (error) {
          // Token might be expired, continue with logout
        }
      }

      res.json({
        success: true,
        message: "Logged out successfully",
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/auth/me
router.get("/me", async (req: Request, res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      throw new AppError("Access token required", 401);
    }

    const decodedToken = await admin.auth().verifyIdToken(token);
    const user = await prisma.user.findUnique({
      where: { firebaseUid: decodedToken.uid },
      include: { tutorProfile: true },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    res.json({
      success: true,
      data: { user },
    });
  } catch (error) {
    next(error);
  }
});

export default router;
