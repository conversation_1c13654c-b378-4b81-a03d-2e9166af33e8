import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { AppError } from '../middleware/errorHandler';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/coaching/sessions - Get coaching sessions
router.get('/sessions', [
  query('courseId').optional().isString(),
  query('upcoming').optional().isBoolean(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { courseId, upcoming } = req.query;
    const where: any = {};

    if (courseId) {
      where.courseId = courseId;
    }

    if (upcoming === 'true') {
      where.scheduledAt = { gte: new Date() };
      where.isActive = true;
    }

    // For students, only show sessions for courses they have terminated subscriptions
    if (req.user!.role === UserRole.STUDENT) {
      const terminatedSubscriptions = await prisma.subscription.findMany({
        where: {
          studentId: req.user!.id,
          status: 'TERMINATED',
        },
        select: { courseId: true }
      });

      const terminatedCourseIds = terminatedSubscriptions.map(sub => sub.courseId);
      where.courseId = { in: terminatedCourseIds };
    }

    // For tutors, only show their own sessions
    if (req.user!.role === UserRole.TUTOR) {
      where.tutorId = req.user!.id;
    }

    const sessions = await prisma.coachingSession.findMany({
      where,
      include: {
        course: {
          select: {
            id: true,
            title: true,
            category: true,
          }
        },
        participants: {
          select: {
            studentId: true,
            joinedAt: true,
          }
        },
        _count: {
          select: { participants: true }
        }
      },
      orderBy: { scheduledAt: 'asc' }
    });

    res.json({
      success: true,
      data: { sessions }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/coaching/sessions - Create coaching session (Tutor/Admin only)
router.post('/sessions', requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  body('courseId').isString().notEmpty(),
  body('title').notEmpty().isLength({ min: 3, max: 200 }),
  body('description').optional().isLength({ max: 1000 }),
  body('meetingLink').isURL(),
  body('scheduledAt').isISO8601(),
  body('duration').isInt({ min: 15, max: 300 }),
  body('maxStudents').isInt({ min: 1, max: 100 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { courseId, title, description, meetingLink, scheduledAt, duration, maxStudents } = req.body;

    // Verify course exists
    const course = await prisma.course.findUnique({
      where: { id: courseId }
    });

    if (!course) {
      throw new AppError('Course not found', 404);
    }

    // Check if tutor owns the course (for tutors)
    if (req.user!.role === UserRole.TUTOR && course.tutorId !== req.user!.id) {
      throw new AppError('You can only create sessions for your own courses', 403);
    }

    const session = await prisma.coachingSession.create({
      data: {
        courseId,
        tutorId: req.user!.id,
        title,
        description,
        meetingLink,
        scheduledAt: new Date(scheduledAt),
        duration,
        maxStudents,
      },
      include: {
        course: {
          select: {
            id: true,
            title: true,
            category: true,
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: { session },
      message: 'Coaching session created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/coaching/sessions/:id/join - Join coaching session (Student only)
router.post('/sessions/:id/join', requireRole([UserRole.STUDENT]), [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const studentId = req.user!.id;

    const session = await prisma.coachingSession.findUnique({
      where: { id },
      include: {
        _count: {
          select: { participants: true }
        }
      }
    });

    if (!session) {
      throw new AppError('Coaching session not found', 404);
    }

    if (!session.isActive) {
      throw new AppError('This coaching session is no longer active', 400);
    }

    if (session.scheduledAt < new Date()) {
      throw new AppError('This coaching session has already started', 400);
    }

    if (session._count.participants >= session.maxStudents) {
      throw new AppError('This coaching session is full', 400);
    }

    // Check if student has terminated subscription for this course
    const terminatedSubscription = await prisma.subscription.findUnique({
      where: {
        studentId_courseId: {
          studentId,
          courseId: session.courseId,
        }
      }
    });

    if (!terminatedSubscription || terminatedSubscription.status !== 'TERMINATED') {
      throw new AppError('You can only join coaching sessions for courses where your access was terminated', 403);
    }

    // Check if already joined
    const existingParticipant = await prisma.coachingSessionParticipant.findUnique({
      where: {
        sessionId_studentId: {
          sessionId: id,
          studentId,
        }
      }
    });

    if (existingParticipant) {
      throw new AppError('You have already joined this coaching session', 400);
    }

    const participant = await prisma.coachingSessionParticipant.create({
      data: {
        sessionId: id,
        studentId,
      }
    });

    res.json({
      success: true,
      data: { participant },
      message: 'Successfully joined coaching session'
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/coaching/failed-students/:courseId - Get students who failed course (Tutor/Admin only)
router.get('/failed-students/:courseId', requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  param('courseId').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { courseId } = req.params;

    // Verify course exists and user has permission
    const course = await prisma.course.findUnique({
      where: { id: courseId }
    });

    if (!course) {
      throw new AppError('Course not found', 404);
    }

    if (req.user!.role === UserRole.TUTOR && course.tutorId !== req.user!.id) {
      throw new AppError('You can only view failed students for your own courses', 403);
    }

    const failedStudents = await prisma.subscription.findMany({
      where: {
        courseId,
        status: 'TERMINATED',
      },
      select: {
        id: true,
        studentId: true,
        failureCount: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    res.json({
      success: true,
      data: { failedStudents }
    });
  } catch (error) {
    next(error);
  }
});

export default router;