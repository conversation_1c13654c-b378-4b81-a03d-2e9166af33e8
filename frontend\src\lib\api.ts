import axios from 'axios';
import { auth } from './firebase';

// Create axios instance
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:80',
  timeout: 30000,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    const user = auth.currentUser;
    if (user) {
      const token = await user.getIdToken();
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

export default api;

// API endpoints
export const endpoints = {
  // Auth endpoints
  auth: {
    verifyToken: '/api/auth/verify-token',
    logout: '/api/auth/logout',
    me: '/api/auth/me',
  },
  
  // User endpoints
  users: {
    list: '/api/users',
    get: (id: string) => `/api/users/${id}`,
    update: (id: string) => `/api/users/${id}`,
    tutorApplication: '/api/users/tutor-application',
  },
  
  // Admin endpoints
  admin: {
    tutorApplications: '/api/admin/tutor-applications',
    approveTutor: (id: string) => `/api/admin/tutors/${id}/approve`,
    rejectTutor: (id: string) => `/api/admin/tutors/${id}/reject`,
    createUser: '/api/admin/users',
    analytics: '/api/admin/analytics',
  },
  
  // Course endpoints
  courses: {
    list: '/api/courses',
    get: (id: string) => `/api/courses/${id}`,
    create: '/api/courses',
    update: (id: string) => `/api/courses/${id}`,
    publish: (id: string) => `/api/courses/${id}/publish`,
  },
  
  // Lesson endpoints
  lessons: {
    get: (id: string) => `/api/lessons/${id}`,
    progress: (id: string) => `/api/lessons/${id}/progress`,
    create: '/api/lessons',
  },
  
  // Assignment endpoints
  assignments: {
    get: (id: string) => `/api/assignments/${id}`,
    submit: (id: string) => `/api/assignments/${id}/submit`,
    create: '/api/assignments',
  },
  
  // Progress endpoints
  progress: {
    dashboard: '/api/progress/dashboard',
    course: (id: string) => `/api/progress/course/${id}`,
  },
  
  // Coaching endpoints
  coaching: {
    sessions: '/api/coaching/sessions',
    createSession: '/api/coaching/sessions',
    joinSession: (id: string) => `/api/coaching/sessions/${id}/join`,
    failedStudents: (courseId: string) => `/api/coaching/failed-students/${courseId}`,
  },
  
  // Payment endpoints
  payments: {
    createInvoice: '/api/payments/create-invoice',
    get: (id: string) => `/api/payments/${id}`,
    list: '/api/payments',
    cancel: (id: string) => `/api/payments/${id}/cancel`,
  },
  
  // Subscription endpoints
  subscriptions: {
    my: '/api/subscriptions/my',
    get: (id: string) => `/api/subscriptions/${id}`,
    cancel: (id: string) => `/api/subscriptions/${id}/cancel`,
    list: '/api/subscriptions',
  },
  
  // Analytics endpoints
  analytics: {
    dashboard: '/api/analytics/dashboard',
    course: (id: string) => `/api/analytics/courses/${id}`,
    tutor: (id: string) => `/api/analytics/tutors/${id}`,
    student: (id: string) => `/api/analytics/students/${id}`,
    revenue: '/api/analytics/revenue',
  },
  
  // Reports endpoints
  reports: {
    students: '/api/reports/students',
    courses: '/api/reports/courses',
    revenue: '/api/reports/revenue',
    systemHealth: '/api/reports/system-health',
  },
  
  // Video endpoints
  videos: {
    upload: '/api/videos/upload',
    stream: (id: string) => `/api/videos/${id}/stream`,
    analytics: (id: string) => `/api/videos/${id}/analytics`,
    delete: (id: string) => `/api/videos/${id}`,
  },
};