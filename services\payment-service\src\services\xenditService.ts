import axios from 'axios';
import crypto from 'crypto';

interface XenditInvoiceRequest {
  external_id: string;
  amount: number;
  payer_email: string;
  description: string;
  invoice_duration?: number;
  success_redirect_url?: string;
  failure_redirect_url?: string;
  currency?: string;
  items?: Array<{
    name: string;
    quantity: number;
    price: number;
    category?: string;
  }>;
}

interface XenditInvoiceResponse {
  id: string;
  external_id: string;
  user_id: string;
  status: string;
  merchant_name: string;
  amount: number;
  payer_email: string;
  description: string;
  expiry_date: string;
  invoice_url: string;
  available_banks: Array<any>;
  available_retail_outlets: Array<any>;
  available_ewallets: Array<any>;
  should_exclude_credit_card: boolean;
  should_send_email: boolean;
  created: string;
  updated: string;
  currency: string;
}

interface XenditWebhookPayload {
  id: string;
  external_id: string;
  user_id: string;
  is_high: boolean;
  payment_method: string;
  status: string;
  merchant_name: string;
  amount: number;
  paid_amount: number;
  bank_code: string;
  paid_at: string;
  payer_email: string;
  description: string;
  adjusted_received_amount: number;
  fees_paid_amount: number;
  updated: string;
  created: string;
  currency: string;
  payment_channel: string;
  payment_destination: string;
}

class XenditService {
  private secretKey: string;
  private webhookToken: string;
  private baseUrl = 'https://api.xendit.co';

  constructor() {
    this.secretKey = process.env.XENDIT_SECRET_KEY!;
    this.webhookToken = process.env.XENDIT_WEBHOOK_TOKEN!;

    if (!this.secretKey || !this.webhookToken) {
      throw new Error('Xendit configuration missing. Please check environment variables.');
    }
  }

  /**
   * Create payment invoice
   */
  async createInvoice(invoiceData: XenditInvoiceRequest): Promise<XenditInvoiceResponse> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/v2/invoices`,
        {
          ...invoiceData,
          currency: invoiceData.currency || 'IDR',
          invoice_duration: invoiceData.invoice_duration || 86400, // 24 hours default
        },
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Xendit create invoice error:', error.response?.data || error.message);
      throw new Error('Failed to create payment invoice');
    }
  }

  /**
   * Get invoice details
   */
  async getInvoice(invoiceId: string): Promise<XenditInvoiceResponse> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/v2/invoices/${invoiceId}`,
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Xendit get invoice error:', error.response?.data || error.message);
      throw new Error('Failed to get invoice details');
    }
  }

  /**
   * Verify webhook signature
   */
  verifyWebhookSignature(rawBody: string, signature: string): boolean {
    try {
      const computedSignature = crypto
        .createHmac('sha256', this.webhookToken)
        .update(rawBody)
        .digest('hex');

      return crypto.timingSafeEqual(
        Buffer.from(signature, 'hex'),
        Buffer.from(computedSignature, 'hex')
      );
    } catch (error) {
      console.error('Webhook signature verification error:', error);
      return false;
    }
  }

  /**
   * Process webhook payload
   */
  processWebhook(payload: XenditWebhookPayload) {
    return {
      invoiceId: payload.id,
      externalId: payload.external_id,
      status: payload.status,
      amount: payload.amount,
      paidAmount: payload.paid_amount,
      paymentMethod: payload.payment_method,
      paidAt: payload.paid_at ? new Date(payload.paid_at) : null,
      currency: payload.currency,
      payerEmail: payload.payer_email,
      fees: payload.fees_paid_amount,
    };
  }

  /**
   * Create refund
   */
  async createRefund(paymentId: string, amount: number, reason: string) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/refunds`,
        {
          invoice_id: paymentId,
          amount,
          reason,
        },
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
            'Content-Type': 'application/json',
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Xendit refund error:', error.response?.data || error.message);
      throw new Error('Failed to create refund');
    }
  }

  /**
   * Get available payment methods
   */
  async getPaymentMethods(amount: number, currency = 'IDR') {
    try {
      const response = await axios.get(
        `${this.baseUrl}/available_payment_methods`,
        {
          params: { amount, currency },
          headers: {
            'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Xendit payment methods error:', error.response?.data || error.message);
      throw new Error('Failed to get payment methods');
    }
  }

  /**
   * Expire invoice
   */
  async expireInvoice(invoiceId: string) {
    try {
      const response = await axios.post(
        `${this.baseUrl}/v2/invoices/${invoiceId}/expire`,
        {},
        {
          headers: {
            'Authorization': `Basic ${Buffer.from(this.secretKey + ':').toString('base64')}`,
          },
        }
      );

      return response.data;
    } catch (error: any) {
      console.error('Xendit expire invoice error:', error.response?.data || error.message);
      throw new Error('Failed to expire invoice');
    }
  }
}

export default new XenditService();