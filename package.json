{"name": "time-academy-microservices", "version": "1.0.0", "description": "Online English Course Platform with Microservices Architecture", "private": true, "workspaces": ["services/*"], "scripts": {"dev": "docker-compose up --build", "dev:services": "docker-compose up --build auth-service course-service payment-service analytics-service", "build": "docker-compose build", "test": "npm run test --workspaces", "lint": "npm run lint --workspaces", "db:migrate": "npm run db:migrate --workspaces", "db:seed": "npm run db:seed --workspaces"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}