'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import api, { endpoints } from '@/lib/api';
import { 
  BookO<PERSON>, 
  Clock, 
  AlertTriangle, 
  Award, 
  TrendingUp,
  Calendar,
  Users,
  Play,
  CheckCircle,
  XCircle
} from 'lucide-react';
import toast from 'react-hot-toast';

interface DashboardData {
  subscriptions: Array<{
    subscription: {
      id: string;
      status: string;
      failureCount: number;
      startDate: string;
      endDate: string;
      progress: number;
    };
    course: {
      id: string;
      title: string;
      category: string;
      durationDays: number;
    };
    stats: {
      totalModules: number;
      completedModules: number;
      totalLessons: number;
      completedLessons: number;
      totalAssignments: number;
      passedAssignments: number;
      failedAssignments: number;
      progressPercentage: number;
    };
    pacing: {
      daysPassed: number;
      expectedModules: number;
      isPacingBehind: boolean;
      warningMessage: string | null;
    };
  }>;
  availableCoachingSessions: Array<{
    id: string;
    title: string;
    description: string;
    scheduledAt: string;
    duration: number;
    maxStudents: number;
    course: {
      id: string;
      title: string;
      category: string;
    };
    _count: {
      participants: number;
    };
  }>;
  summary: {
    totalActiveCourses: number;
    totalTerminatedCourses: number;
    coursesWithPacingWarnings: number;
  };
}

export default function StudentDashboard() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!loading && (!user || user.role !== 'STUDENT')) {
      router.push('/login');
      return;
    }

    if (user) {
      fetchDashboardData();
    }
  }, [user, loading, router]);

  const fetchDashboardData = async () => {
    try {
      const response = await api.get(endpoints.progress.dashboard);
      if (response.data.success) {
        setDashboardData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  const joinCoachingSession = async (sessionId: string) => {
    try {
      const response = await api.post(endpoints.coaching.joinSession(sessionId));
      if (response.data.success) {
        toast.success('Successfully joined coaching session!');
        fetchDashboardData(); // Refresh data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to join coaching session');
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      ACTIVE: 'badge-success',
      TERMINATED: 'badge-error',
      EXPIRED: 'badge-gray',
      CANCELLED: 'badge-warning',
    };
    return badges[status as keyof typeof badges] || 'badge-gray';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const calculateDaysRemaining = (endDate: string) => {
    const end = new Date(endDate);
    const now = new Date();
    const diffTime = end.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner w-8 h-8"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-500">Failed to load dashboard data</p>
          <button 
            onClick={fetchDashboardData}
            className="btn-primary mt-4"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Welcome back, {user?.displayName}!
              </h1>
              <p className="text-gray-600">Track your progress and continue learning</p>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/courses')}
                className="btn-primary"
              >
                Browse Courses
              </button>
              <button
                onClick={() => router.push('/student/profile')}
                className="btn-outline"
              >
                Profile
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Summary Cards */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <div className="card">
            <div className="flex items-center">
              <BookOpen className="h-8 w-8 text-primary-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Active Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.summary.totalActiveCourses}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-warning-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Pacing Warnings</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.summary.coursesWithPacingWarnings}
                </p>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-error-600 mr-3" />
              <div>
                <p className="text-sm text-gray-500">Terminated Courses</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.summary.totalTerminatedCourses}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Active Courses */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Your Courses</h2>
          
          {dashboardData.subscriptions.length === 0 ? (
            <div className="card text-center py-12">
              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No courses yet</h3>
              <p className="text-gray-500 mb-4">Start your English learning journey today!</p>
              <button
                onClick={() => router.push('/courses')}
                className="btn-primary"
              >
                Browse Courses
              </button>
            </div>
          ) : (
            <div className="grid gap-6">
              {dashboardData.subscriptions.map((item) => (
                <div key={item.subscription.id} className="card">
                  <div className="flex justify-between items-start mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {item.course.title}
                      </h3>
                      <p className="text-gray-600">{item.course.category}</p>
                    </div>
                    <span className={`badge ${getStatusBadge(item.subscription.status)}`}>
                      {item.subscription.status}
                    </span>
                  </div>

                  {/* Pacing Warning */}
                  {item.pacing.isPacingBehind && (
                    <div className="bg-warning-50 border border-warning-200 rounded-md p-4 mb-4">
                      <div className="flex items-center">
                        <AlertTriangle className="h-5 w-5 text-warning-600 mr-2" />
                        <p className="text-warning-800 text-sm">
                          {item.pacing.warningMessage}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Progress Bar */}
                  <div className="mb-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Progress</span>
                      <span>{item.stats.progressPercentage}%</span>
                    </div>
                    <div className="progress-bar">
                      <div 
                        className="progress-fill"
                        style={{ width: `${item.stats.progressPercentage}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Stats Grid */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">
                        {item.stats.completedLessons}
                      </p>
                      <p className="text-sm text-gray-500">
                        of {item.stats.totalLessons} lessons
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-success-600">
                        {item.stats.passedAssignments}
                      </p>
                      <p className="text-sm text-gray-500">passed</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-error-600">
                        {item.stats.failedAssignments}
                      </p>
                      <p className="text-sm text-gray-500">failed</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-900">
                        {calculateDaysRemaining(item.subscription.endDate)}
                      </p>
                      <p className="text-sm text-gray-500">days left</p>
                    </div>
                  </div>

                  {/* Failure Count Warning */}
                  {item.subscription.failureCount > 0 && (
                    <div className="bg-error-50 border border-error-200 rounded-md p-3 mb-4">
                      <p className="text-error-800 text-sm">
                        ⚠️ Assignment failures: {item.subscription.failureCount}/3
                        {item.subscription.failureCount >= 2 && (
                          <span className="font-semibold"> - Be careful! One more failure will terminate your access.</span>
                        )}
                      </p>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex space-x-3">
                    {item.subscription.status === 'ACTIVE' ? (
                      <button
                        onClick={() => router.push(`/student/courses/${item.course.id}`)}
                        className="btn-primary flex items-center"
                      >
                        <Play className="h-4 w-4 mr-2" />
                        Continue Learning
                      </button>
                    ) : (
                      <button
                        onClick={() => router.push(`/student/courses/${item.course.id}`)}
                        className="btn-outline flex items-center"
                        disabled
                      >
                        <XCircle className="h-4 w-4 mr-2" />
                        Access Terminated
                      </button>
                    )}
                    <button
                      onClick={() => router.push(`/student/progress/${item.course.id}`)}
                      className="btn-outline flex items-center"
                    >
                      <TrendingUp className="h-4 w-4 mr-2" />
                      View Progress
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Coaching Sessions */}
        {dashboardData.availableCoachingSessions.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              Available Coaching Sessions
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              {dashboardData.availableCoachingSessions.map((session) => (
                <div key={session.id} className="card">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {session.title}
                    </h3>
                    <span className="badge badge-primary">Free</span>
                  </div>
                  
                  <p className="text-gray-600 mb-3">{session.description}</p>
                  
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <Calendar className="h-4 w-4 mr-2" />
                    {formatDate(session.scheduledAt)} • {session.duration} minutes
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-500 mb-4">
                    <Users className="h-4 w-4 mr-2" />
                    {session._count.participants}/{session.maxStudents} participants
                  </div>
                  
                  <button
                    onClick={() => joinCoachingSession(session.id)}
                    className="btn-primary w-full"
                    disabled={session._count.participants >= session.maxStudents}
                  >
                    {session._count.participants >= session.maxStudents 
                      ? 'Session Full' 
                      : 'Join Session'
                    }
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}