'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import api, { endpoints } from '@/lib/api';
import { 
  Play, 
  Pause, 
  Volume2, 
  Maximize, 
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowLeft,
  ArrowRight,
  BookOpen
} from 'lucide-react';
import toast from 'react-hot-toast';

interface Lesson {
  id: string;
  title: string;
  description: string;
  videoUrl?: string;
  videoDuration?: number;
  bunnyVideoId?: string;
  assignments: Array<{
    id: string;
    title: string;
    type: string;
    videoTimestamp?: number;
    timeLimit: number;
    passingScore: number;
    questions: any[];
  }>;
  progress: Array<{
    completedAt: string;
    watchedDuration: number;
  }>;
}

interface Assignment {
  id: string;
  title: string;
  type: string;
  questions: Array<{
    id: string;
    question: string;
    type: string;
    options?: string[];
    correctAnswer?: string;
    keyTerms?: string[];
    minWords?: number;
  }>;
  timeLimit: number;
  passingScore: number;
}

export default function StudentCoursePage() {
  const { user } = useAuth();
  const router = useRouter();
  const params = useParams();
  const courseId = params.id as string;
  
  const [currentLesson, setCurrentLesson] = useState<Lesson | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showAssignment, setShowAssignment] = useState(false);
  const [currentAssignment, setCurrentAssignment] = useState<Assignment | null>(null);
  const [assignmentAnswers, setAssignmentAnswers] = useState<any[]>([]);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (!user || user.role !== 'STUDENT') {
      router.push('/login');
      return;
    }

    // For demo, we'll load the first lesson
    // In a real app, you'd get the lesson ID from the URL or course progress
    fetchLesson('demo-lesson-id');
  }, [user, router]);

  const fetchLesson = async (lessonId: string) => {
    try {
      const response = await api.get(endpoints.lessons.get(lessonId));
      if (response.data.success) {
        setCurrentLesson(response.data.data.lesson);
      }
    } catch (error) {
      console.error('Error fetching lesson:', error);
      toast.error('Failed to load lesson');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVideoTimeUpdate = (time: number) => {
    setCurrentTime(time);
    
    // Check for assignments at current timestamp
    if (currentLesson) {
      const assignment = currentLesson.assignments.find(
        a => a.videoTimestamp && Math.abs(a.videoTimestamp - time) < 1
      );
      
      if (assignment && !showAssignment) {
        setCurrentAssignment(assignment as Assignment);
        setShowAssignment(true);
        setIsPlaying(false);
        setTimeRemaining(assignment.timeLimit * 60); // Convert minutes to seconds
        setAssignmentAnswers(new Array(assignment.questions?.length || 0).fill(''));
      }
    }
  };

  const handleAssignmentSubmit = async () => {
    if (!currentAssignment) return;

    try {
      setIsSubmitting(true);
      const response = await api.post(
        endpoints.assignments.submit(currentAssignment.id),
        { answers: assignmentAnswers }
      );

      if (response.data.success) {
        const { score, passed, terminated } = response.data.data.submission;
        
        if (terminated) {
          toast.error('Course access terminated due to 3 assignment failures');
          router.push('/student/dashboard');
          return;
        }

        if (passed) {
          toast.success(`Assignment passed! Score: ${score}%`);
        } else {
          toast.error(`Assignment failed. Score: ${score}%`);
        }

        setShowAssignment(false);
        setCurrentAssignment(null);
        setIsPlaying(true);
      }
    } catch (error: any) {
      toast.error(error.response?.data?.error || 'Failed to submit assignment');
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateProgress = async (watchedDuration: number, completed = false) => {
    if (!currentLesson) return;

    try {
      await api.post(endpoints.lessons.progress(currentLesson.id), {
        watchedDuration,
        completed
      });
    } catch (error) {
      console.error('Error updating progress:', error);
    }
  };

  // Timer for assignment
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (showAssignment && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleAssignmentSubmit(); // Auto-submit when time runs out
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => clearInterval(interval);
  }, [showAssignment, timeRemaining]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="spinner w-8 h-8"></div>
      </div>
    );
  }

  if (!currentLesson) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Lesson not found</h1>
          <button onClick={() => router.push('/student/dashboard')} className="btn-primary">
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Video Player */}
      <div className="relative">
        <div className="aspect-video bg-gray-900 flex items-center justify-center">
          {currentLesson.videoUrl || currentLesson.bunnyVideoId ? (
            <div className="w-full h-full relative">
              {/* Video would be embedded here */}
              <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                <div className="text-center text-white">
                  <Play className="h-16 w-16 mx-auto mb-4" />
                  <p className="text-lg">{currentLesson.title}</p>
                  <p className="text-sm opacity-75">Video Player Integration</p>
                </div>
              </div>

              {/* Video Controls */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="text-white hover:text-primary-400"
                  >
                    {isPlaying ? <Pause className="h-6 w-6" /> : <Play className="h-6 w-6" />}
                  </button>
                  
                  <div className="flex-1">
                    <div className="bg-gray-600 rounded-full h-1">
                      <div 
                        className="bg-primary-500 h-1 rounded-full"
                        style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <span className="text-white text-sm">
                    {formatTime(currentTime)} / {formatTime(duration)}
                  </span>
                  
                  <Volume2 className="h-5 w-5 text-white" />
                  <Maximize className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center text-white">
              <BookOpen className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg">No video available</p>
            </div>
          )}
        </div>

        {/* Assignment Overlay */}
        {showAssignment && currentAssignment && (
          <div className="assignment-overlay">
            <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold text-gray-900">
                    {currentAssignment.title}
                  </h2>
                  <div className="flex items-center text-error-600">
                    <Clock className="h-5 w-5 mr-2" />
                    <span className="font-mono text-lg">
                      {formatTime(timeRemaining)}
                    </span>
                  </div>
                </div>

                <div className="bg-warning-50 border border-warning-200 rounded-md p-4 mb-6">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-warning-600 mr-2" />
                    <p className="text-warning-800 text-sm">
                      You must score {currentAssignment.passingScore}% to pass. 
                      Failing 3 assignments will terminate your course access.
                    </p>
                  </div>
                </div>

                <div className="space-y-6">
                  {currentAssignment.questions?.map((question, index) => (
                    <div key={question.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                      <h3 className="font-semibold text-gray-900 mb-3">
                        Question {index + 1}: {question.question}
                      </h3>

                      {question.type === 'MULTIPLE_CHOICE' && question.options && (
                        <div className="space-y-2">
                          {question.options.map((option, optionIndex) => (
                            <label key={optionIndex} className="flex items-center">
                              <input
                                type="radio"
                                name={`question-${index}`}
                                value={option}
                                checked={assignmentAnswers[index] === option}
                                onChange={(e) => {
                                  const newAnswers = [...assignmentAnswers];
                                  newAnswers[index] = e.target.value;
                                  setAssignmentAnswers(newAnswers);
                                }}
                                className="mr-3"
                              />
                              <span>{option}</span>
                            </label>
                          ))}
                        </div>
                      )}

                      {question.type === 'SHORT_ANSWER' && (
                        <input
                          type="text"
                          value={assignmentAnswers[index] || ''}
                          onChange={(e) => {
                            const newAnswers = [...assignmentAnswers];
                            newAnswers[index] = e.target.value;
                            setAssignmentAnswers(newAnswers);
                          }}
                          className="input w-full"
                          placeholder="Enter your answer..."
                        />
                      )}

                      {question.type === 'ESSAY' && (
                        <div>
                          <textarea
                            value={assignmentAnswers[index] || ''}
                            onChange={(e) => {
                              const newAnswers = [...assignmentAnswers];
                              newAnswers[index] = e.target.value;
                              setAssignmentAnswers(newAnswers);
                            }}
                            className="input w-full h-32"
                            placeholder="Write your essay here..."
                          />
                          {question.minWords && (
                            <p className="text-sm text-gray-500 mt-1">
                              Minimum {question.minWords} words required
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                <div className="flex justify-end space-x-4 mt-6 pt-6 border-t border-gray-200">
                  <button
                    onClick={handleAssignmentSubmit}
                    disabled={isSubmitting || timeRemaining === 0}
                    className="btn-primary"
                  >
                    {isSubmitting ? (
                      <div className="spinner w-5 h-5 mr-2"></div>
                    ) : (
                      <CheckCircle className="h-5 w-5 mr-2" />
                    )}
                    {isSubmitting ? 'Submitting...' : 'Submit Assignment'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Course Navigation */}
      <div className="bg-white border-t">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/student/dashboard')}
                className="btn-outline flex items-center"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </button>
              
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  {currentLesson.title}
                </h1>
                <p className="text-sm text-gray-500">
                  {currentLesson.description}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button className="btn-outline flex items-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </button>
              <button className="btn-primary flex items-center">
                Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}