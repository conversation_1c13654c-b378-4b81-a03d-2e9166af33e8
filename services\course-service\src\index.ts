import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import cron from 'node-cron';

import { errorHandler } from './middleware/errorHandler';
import { authenticateToken } from './middleware/auth';
import courseRoutes from './routes/courses';
import lessonRoutes from './routes/lessons';
import moduleRoutes from './routes/modules';
import assignmentRoutes from './routes/assignments';
import progressRoutes from './routes/progress';
import coachingRoutes from './routes/coaching';
import videoRoutes from './routes/videos';
import { checkSubscriptionExpiry, checkStudentPacing } from './services/cronJobs';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3002;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 500, // Higher limit for learning service
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://timeacademy.com']
    : true,
  credentials: true,
}));
app.use(limiter);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Course Service is healthy',
    timestamp: new Date().toISOString(),
    service: 'course-service',
    version: '1.0.0'
  });
});

// API Routes
app.use('/api/courses', courseRoutes);
app.use('/api/lessons', authenticateToken, lessonRoutes);
app.use('/api/modules', authenticateToken, moduleRoutes);
app.use('/api/assignments', authenticateToken, assignmentRoutes);
app.use('/api/progress', authenticateToken, progressRoutes);
app.use('/api/coaching', authenticateToken, coachingRoutes);
app.use('/api/videos', authenticateToken, videoRoutes);

// Error handling
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Route not found'
  });
});

// Cron jobs
if (process.env.NODE_ENV === 'production') {
  // Check for expired subscriptions daily at midnight
  cron.schedule('0 0 * * *', checkSubscriptionExpiry);
  
  // Check student pacing daily at 6 AM
  cron.schedule('0 6 * * *', checkStudentPacing);
}

app.listen(PORT, () => {
  console.log(`🚀 Course Service running on port ${PORT}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`);
});