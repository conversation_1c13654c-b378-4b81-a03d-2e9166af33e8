'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User as FirebaseUser, onAuthStateChanged, signInWithPopup, signOut } from 'firebase/auth';
import { auth, googleProvider } from '@/lib/firebase';
import api, { endpoints } from '@/lib/api';
import toast from 'react-hot-toast';

interface User {
  id: string;
  firebaseUid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: 'STUDENT' | 'TUTOR' | 'ADMIN';
  isActive: boolean;
  tutorProfile?: {
    id: string;
    bio: string;
    expertise: string[];
    isApproved: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  firebaseUser: FirebaseUser | null;
  loading: boolean;
  signInWithGoogle: () => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      setFirebaseUser(firebaseUser);
      
      if (firebaseUser) {
        try {
          // Verify token and get user data from backend
          const idToken = await firebaseUser.getIdToken();
          const response = await api.post(endpoints.auth.verifyToken, { idToken });
          
          if (response.data.success) {
            setUser(response.data.data.user);
          } else {
            throw new Error('Failed to verify user');
          }
        } catch (error) {
          console.error('Error verifying user:', error);
          toast.error('Authentication failed');
          await signOut(auth);
          setUser(null);
        }
      } else {
        setUser(null);
      }
      
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signInWithGoogle = async () => {
    try {
      setLoading(true);
      const result = await signInWithPopup(auth, googleProvider);
      
      // The onAuthStateChanged listener will handle the rest
      toast.success('Successfully signed in!');
    } catch (error: any) {
      console.error('Google sign in error:', error);
      toast.error(error.message || 'Failed to sign in with Google');
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      // Call backend logout endpoint
      await api.post(endpoints.auth.logout);
    } catch (error) {
      console.error('Backend logout error:', error);
    } finally {
      // Always sign out from Firebase
      await signOut(auth);
      setUser(null);
      setFirebaseUser(null);
      toast.success('Successfully signed out');
    }
  };

  const refreshUser = async () => {
    if (firebaseUser) {
      try {
        const response = await api.get(endpoints.auth.me);
        if (response.data.success) {
          setUser(response.data.data.user);
        }
      } catch (error) {
        console.error('Error refreshing user:', error);
      }
    }
  };

  const value = {
    user,
    firebaseUser,
    loading,
    signInWithGoogle,
    logout,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}