{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "./dist", "rootDir": "./src", "module": "CommonJS"}, "include": ["services/*/src/**/*", "services/shared/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}