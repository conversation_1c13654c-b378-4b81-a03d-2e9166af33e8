import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import admin from 'firebase-admin';
import { UserRole } from '../types';

// Extend Express Request interface to include user
export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    role: UserRole;
    name: string;
  };
}

// JWT Authentication middleware
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        success: false,
        error: 'Access token required'
      });
      return;
    }

    // Try JWT first (for service-to-service communication)
    if (process.env.JWT_SECRET) {
      try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;
        req.user = {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role,
          name: decoded.name
        };
        next();
        return;
      } catch (jwtError) {
        // If JWT fails, try Firebase token
      }
    }

    // Try Firebase token verification
    try {
      const decodedToken = await admin.auth().verifyIdToken(token);
      
      // Get additional user info from Firebase
      const userRecord = await admin.auth().getUser(decodedToken.uid);
      
      req.user = {
        id: decodedToken.uid,
        email: decodedToken.email || '',
        role: (decodedToken.role as UserRole) || UserRole.STUDENT,
        name: userRecord.displayName || decodedToken.name || ''
      };
      
      next();
    } catch (firebaseError) {
      console.error('Token verification failed:', firebaseError);
      res.status(403).json({
        success: false,
        error: 'Invalid or expired token'
      });
    }
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication service error'
    });
  }
};

// Role-based authorization middleware
export const requireRole = (allowedRoles: UserRole | UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({
        success: false,
        error: 'Authentication required'
      });
      return;
    }

    const roles = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
    
    if (!roles.includes(req.user.role)) {
      res.status(403).json({
        success: false,
        error: 'Insufficient permissions'
      });
      return;
    }

    next();
  };
};

// Admin only middleware
export const requireAdmin = requireRole(UserRole.ADMIN);

// Instructor or Admin middleware
export const requireInstructorOrAdmin = requireRole([UserRole.INSTRUCTOR, UserRole.ADMIN]);

// Student, Instructor, or Admin middleware (authenticated users)
export const requireAuthenticated = requireRole([UserRole.STUDENT, UserRole.INSTRUCTOR, UserRole.ADMIN]);

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      next();
      return;
    }

    // Try to authenticate, but don't fail if it doesn't work
    try {
      if (process.env.JWT_SECRET) {
        const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;
        req.user = {
          id: decoded.id,
          email: decoded.email,
          role: decoded.role,
          name: decoded.name
        };
      } else {
        const decodedToken = await admin.auth().verifyIdToken(token);
        const userRecord = await admin.auth().getUser(decodedToken.uid);
        
        req.user = {
          id: decodedToken.uid,
          email: decodedToken.email || '',
          role: (decodedToken.role as UserRole) || UserRole.STUDENT,
          name: userRecord.displayName || decodedToken.name || ''
        };
      }
    } catch (error) {
      // Ignore authentication errors for optional auth
      console.log('Optional auth failed, continuing without user:', error.message);
    }

    next();
  } catch (error) {
    console.error('Optional authentication error:', error);
    next(); // Continue even if there's an error
  }
};

// Generate JWT token (for service-to-service communication)
export const generateJWT = (user: {
  id: string;
  email: string;
  role: UserRole;
  name: string;
}): string => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured');
  }

  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role,
      name: user.name
    },
    process.env.JWT_SECRET,
    { expiresIn: '24h' }
  );
};

// Verify JWT token
export const verifyJWT = (token: string): any => {
  if (!process.env.JWT_SECRET) {
    throw new Error('JWT_SECRET not configured');
  }

  return jwt.verify(token, process.env.JWT_SECRET);
};
