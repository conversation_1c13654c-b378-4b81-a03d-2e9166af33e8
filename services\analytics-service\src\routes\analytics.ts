import { Router, Response, NextFunction } from "express";
import { query, param, validationResult } from "express-validator";
import { PrismaClient } from "@prisma/client";
import {
  authenticateToken,
  requireRole,
  AuthenticatedRequest,
} from "../shared/middleware/auth";
import { UserRole } from "../shared/types";
import { AppError } from "../middleware/errorHandler";

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: "Validation failed",
      details: errors.array(),
    });
  }
  next();
};

// GET /api/analytics/dashboard - Admin dashboard analytics
router.get(
  "/dashboard",
  requireRole([UserRole.ADMIN]),
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const today = new Date();
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(today.getDate() - 30);

      // Get latest daily metrics
      const latestMetrics = await prisma.dailyMetrics.findFirst({
        orderBy: { date: "desc" },
      });

      // Get metrics for the last 30 days
      const monthlyMetrics = await prisma.dailyMetrics.findMany({
        where: {
          date: {
            gte: thirtyDaysAgo,
            lte: today,
          },
        },
        orderBy: { date: "asc" },
      });

      // Calculate trends
      const previousMetrics = await prisma.dailyMetrics.findFirst({
        where: {
          date: {
            lt: thirtyDaysAgo,
          },
        },
        orderBy: { date: "desc" },
      });

      const trends = calculateTrends(latestMetrics, previousMetrics);

      // Get top performing courses
      const topCourses = await prisma.courseAnalytics.findMany({
        where: {
          date: {
            gte: thirtyDaysAgo,
          },
        },
        orderBy: { enrollments: "desc" },
        take: 10,
      });

      // Get revenue breakdown
      const revenueData = await prisma.revenueAnalytics.findMany({
        where: {
          date: {
            gte: thirtyDaysAgo,
          },
        },
        orderBy: { date: "asc" },
      });

      res.json({
        success: true,
        data: {
          overview: {
            totalUsers: latestMetrics?.totalUsers || 0,
            activeSubscriptions: latestMetrics?.activeSubscriptions || 0,
            totalRevenue: latestMetrics?.totalRevenue || 0,
            totalCourses: latestMetrics?.totalCourses || 0,
            trends,
          },
          charts: {
            monthlyMetrics,
            topCourses,
            revenueData,
          },
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/analytics/courses/:id - Course performance analytics
router.get(
  "/courses/:id",
  requireRole([UserRole.TUTOR, UserRole.ADMIN]),
  [
    param("id").isString().notEmpty(),
    query("dateFrom").optional().isISO8601(),
    query("dateTo").optional().isISO8601(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { dateFrom, dateTo } = req.query;

      const startDate = dateFrom
        ? new Date(dateFrom as string)
        : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const endDate = dateTo ? new Date(dateTo as string) : new Date();

      // Get course analytics
      const courseAnalytics = await prisma.courseAnalytics.findMany({
        where: {
          courseId: id,
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: { date: "asc" },
      });

      // Get latest metrics
      const latestMetrics = await prisma.courseAnalytics.findFirst({
        where: { courseId: id },
        orderBy: { date: "desc" },
      });

      // Calculate summary statistics
      const totalEnrollments = courseAnalytics.reduce(
        (sum, metric) => sum + metric.enrollments,
        0
      );
      const totalRevenue = courseAnalytics.reduce(
        (sum, metric) => sum + Number(metric.revenue),
        0
      );
      const averageProgress =
        courseAnalytics.length > 0
          ? courseAnalytics.reduce(
              (sum, metric) => sum + Number(metric.averageProgress),
              0
            ) / courseAnalytics.length
          : 0;

      res.json({
        success: true,
        data: {
          courseId: id,
          summary: {
            totalEnrollments,
            activeStudents: latestMetrics?.activeStudents || 0,
            completedStudents: latestMetrics?.completedStudents || 0,
            terminatedStudents: latestMetrics?.terminatedStudents || 0,
            averageProgress: Math.round(averageProgress * 100) / 100,
            totalRevenue,
            assignmentPassRate: latestMetrics?.assignmentPassRate || 0,
          },
          timeline: courseAnalytics,
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/analytics/tutors/:id - Tutor performance analytics
router.get(
  "/tutors/:id",
  requireRole([UserRole.TUTOR, UserRole.ADMIN]),
  [
    param("id").isString().notEmpty(),
    query("dateFrom").optional().isISO8601(),
    query("dateTo").optional().isISO8601(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { dateFrom, dateTo } = req.query;

      // Check if user can access this tutor's data
      if (req.user!.role === UserRole.TUTOR && req.user!.id !== id) {
        throw new AppError("You can only view your own analytics", 403);
      }

      const startDate = dateFrom
        ? new Date(dateFrom as string)
        : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const endDate = dateTo ? new Date(dateTo as string) : new Date();

      // Get tutor analytics
      const tutorAnalytics = await prisma.tutorAnalytics.findMany({
        where: {
          tutorId: id,
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: { date: "asc" },
      });

      // Get latest metrics
      const latestMetrics = await prisma.tutorAnalytics.findFirst({
        where: { tutorId: id },
        orderBy: { date: "desc" },
      });

      res.json({
        success: true,
        data: {
          tutorId: id,
          summary: {
            totalCourses: latestMetrics?.totalCourses || 0,
            publishedCourses: latestMetrics?.publishedCourses || 0,
            totalStudents: latestMetrics?.totalStudents || 0,
            activeStudents: latestMetrics?.activeStudents || 0,
            completedStudents: latestMetrics?.completedStudents || 0,
            terminatedStudents: latestMetrics?.terminatedStudents || 0,
            totalRevenue: latestMetrics?.totalRevenue || 0,
            coachingSessions: latestMetrics?.coachingSessions || 0,
            averageRating: latestMetrics?.averageRating || 0,
          },
          timeline: tutorAnalytics,
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/analytics/students/:id - Student performance analytics
router.get(
  "/students/:id",
  requireRole([UserRole.STUDENT, UserRole.TUTOR, UserRole.ADMIN]),
  [
    param("id").isString().notEmpty(),
    query("dateFrom").optional().isISO8601(),
    query("dateTo").optional().isISO8601(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { id } = req.params;
      const { dateFrom, dateTo } = req.query;

      // Check if user can access this student's data
      if (req.user!.role === UserRole.STUDENT && req.user!.id !== id) {
        throw new AppError("You can only view your own analytics", 403);
      }

      const startDate = dateFrom
        ? new Date(dateFrom as string)
        : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const endDate = dateTo ? new Date(dateTo as string) : new Date();

      // Get student analytics
      const studentAnalytics = await prisma.studentAnalytics.findMany({
        where: {
          studentId: id,
          date: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: { date: "asc" },
      });

      // Get latest metrics
      const latestMetrics = await prisma.studentAnalytics.findFirst({
        where: { studentId: id },
        orderBy: { date: "desc" },
      });

      res.json({
        success: true,
        data: {
          studentId: id,
          summary: {
            activeCourses: latestMetrics?.activeCourses || 0,
            completedCourses: latestMetrics?.completedCourses || 0,
            terminatedCourses: latestMetrics?.terminatedCourses || 0,
            totalWatchTime: latestMetrics?.totalWatchTime || 0,
            assignmentsCompleted: latestMetrics?.assignmentsCompleted || 0,
            assignmentsPassed: latestMetrics?.assignmentsPassed || 0,
            assignmentsFailed: latestMetrics?.assignmentsFailed || 0,
            averageScore: latestMetrics?.averageScore || 0,
            certificatesEarned: latestMetrics?.certificatesEarned || 0,
          },
          timeline: studentAnalytics,
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// GET /api/analytics/revenue - Revenue analytics
router.get(
  "/revenue",
  requireRole([UserRole.ADMIN]),
  [
    query("dateFrom").optional().isISO8601(),
    query("dateTo").optional().isISO8601(),
    query("courseId").optional().isString(),
    query("tutorId").optional().isString(),
  ],
  handleValidationErrors,
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { dateFrom, dateTo, courseId, tutorId } = req.query;

      const startDate = dateFrom
        ? new Date(dateFrom as string)
        : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const endDate = dateTo ? new Date(dateTo as string) : new Date();

      const where: any = {
        date: {
          gte: startDate,
          lte: endDate,
        },
      };

      if (courseId) where.courseId = courseId;
      if (tutorId) where.tutorId = tutorId;

      const revenueData = await prisma.revenueAnalytics.findMany({
        where,
        orderBy: { date: "asc" },
      });

      // Calculate totals
      const totals = revenueData.reduce(
        (acc, item) => ({
          subscriptions: acc.subscriptions + item.subscriptions,
          grossRevenue: acc.grossRevenue + Number(item.grossRevenue),
          platformFee: acc.platformFee + Number(item.platformFee),
          tutorEarnings: acc.tutorEarnings + Number(item.tutorEarnings),
          refunds: acc.refunds + Number(item.refunds),
          netRevenue: acc.netRevenue + Number(item.netRevenue),
        }),
        {
          subscriptions: 0,
          grossRevenue: 0,
          platformFee: 0,
          tutorEarnings: 0,
          refunds: 0,
          netRevenue: 0,
        }
      );

      res.json({
        success: true,
        data: {
          totals,
          timeline: revenueData,
        },
      });
    } catch (error) {
      next(error);
    }
  }
);

// Helper function to calculate trends
function calculateTrends(current: any, previous: any) {
  if (!current || !previous) {
    return {
      users: 0,
      subscriptions: 0,
      revenue: 0,
      courses: 0,
    };
  }

  return {
    users: calculatePercentageChange(current.totalUsers, previous.totalUsers),
    subscriptions: calculatePercentageChange(
      current.activeSubscriptions,
      previous.activeSubscriptions
    ),
    revenue: calculatePercentageChange(
      Number(current.totalRevenue),
      Number(previous.totalRevenue)
    ),
    courses: calculatePercentageChange(
      current.totalCourses,
      previous.totalCourses
    ),
  };
}

function calculatePercentageChange(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return Math.round(((current - previous) / previous) * 100);
}

export default router;
