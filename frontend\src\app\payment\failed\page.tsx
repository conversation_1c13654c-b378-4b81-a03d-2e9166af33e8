'use client';

import { useRouter } from 'next/navigation';
import { XCir<PERSON>, ArrowLeft, RefreshCw } from 'lucide-react';

export default function PaymentFailedPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="card text-center">
          <div className="mb-6">
            <XCircle className="h-16 w-16 text-error-600 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Payment Failed
            </h1>
            <p className="text-gray-600">
              We couldn't process your payment. Please try again.
            </p>
          </div>

          <div className="bg-error-50 border border-error-200 rounded-lg p-4 mb-6">
            <p className="text-error-800 text-sm">
              Your payment was not completed. No charges have been made to your account.
            </p>
          </div>

          <div className="space-y-3">
            <button
              onClick={() => router.back()}
              className="btn-primary w-full flex items-center justify-center"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </button>
            
            <button
              onClick={() => router.push('/courses')}
              className="btn-outline w-full flex items-center justify-center"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Courses
            </button>
          </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-gray-500">
              Need help? <a href="/contact" className="text-primary-600 hover:text-primary-500">Contact Support</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}