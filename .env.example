# Firebase Configuration
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----<PERSON>ND PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-here

# Bunny.net Configuration
BUNNY_API_KEY=your-bunny-api-key
BUNNY_STREAM_API_KEY=your-bunny-stream-api-key
BUNNY_LIBRARY_ID=your-library-id

# Xendit Configuration
XENDIT_SECRET_KEY=your-xendit-secret-key
XENDIT_WEBHOOK_TOKEN=your-xendit-webhook-token

# Frontend URL
FRONTEND_URL=http://localhost:3000