{"name": "time-academy-frontend", "version": "1.0.0", "description": "Time Academy - Online English Course Platform Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.10.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "firebase": "^10.7.0", "axios": "^1.6.0", "react-query": "^3.39.0", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.4.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "@headlessui/react": "^1.7.0", "recharts": "^2.8.0", "react-player": "^2.13.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0"}}