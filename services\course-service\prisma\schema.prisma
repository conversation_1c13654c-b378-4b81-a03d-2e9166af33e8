generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Course {
  id          String   @id @default(cuid())
  title       String
  description String
  category    String
  durationDays Int     @map("duration_days")
  price       Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD")
  isPublished Boolean  @default(false) @map("is_published")
  tutorId     String   @map("tutor_id")
  thumbnailUrl String? @map("thumbnail_url")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relationships
  modules     Module[]
  subscriptions Subscription[]
  coachingSessions CoachingSession[]

  @@map("courses")
}

model Module {
  id          String   @id @default(cuid())
  courseId    String   @map("course_id")
  title       String
  description String?
  orderIndex  Int      @map("order_index")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relationships
  course  Course   @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lessons Lesson[]

  @@map("modules")
}

model Lesson {
  id           String   @id @default(cuid())
  moduleId     String   @map("module_id")
  title        String
  description  String?
  orderIndex   Int      @map("order_index")
  videoUrl     String?  @map("video_url")
  videoDuration Int?    @map("video_duration") // in seconds
  bunnyVideoId String?  @map("bunny_video_id")
  materials    Json?    // PDFs, PPTs, etc.
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relationships
  module      Module       @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  assignments Assignment[]
  progress    StudentProgress[]

  @@map("lessons")
}

model Assignment {
  id               String         @id @default(cuid())
  lessonId         String         @map("lesson_id")
  title            String
  type             AssignmentType
  videoTimestamp   Int?           @map("video_timestamp") // seconds into video
  timeLimit        Int            @map("time_limit") // minutes
  passingScore     Int            @map("passing_score") // percentage
  questions        Json           // Array of questions
  createdAt        DateTime       @default(now()) @map("created_at")
  updatedAt        DateTime       @updatedAt @map("updated_at")

  // Relationships
  lesson     Lesson       @relation(fields: [lessonId], references: [id], onDelete: Cascade)
  submissions AssignmentSubmission[]

  @@map("assignments")
}

model AssignmentSubmission {
  id           String   @id @default(cuid())
  assignmentId String   @map("assignment_id")
  studentId    String   @map("student_id")
  answers      Json     // Student's answers
  score        Int      // Percentage score
  passed       Boolean
  completedAt  DateTime @default(now()) @map("completed_at")

  // Relationships
  assignment Assignment @relation(fields: [assignmentId], references: [id], onDelete: Cascade)

  @@unique([assignmentId, studentId])
  @@map("assignment_submissions")
}

model Subscription {
  id           String             @id @default(cuid())
  studentId    String             @map("student_id")
  courseId     String             @map("course_id")
  status       SubscriptionStatus @default(ACTIVE)
  failureCount Int                @default(0) @map("failure_count")
  startDate    DateTime           @map("start_date")
  endDate      DateTime           @map("end_date")
  progress     Decimal            @default(0) @db.Decimal(5, 2) // Percentage
  createdAt    DateTime           @default(now()) @map("created_at")
  updatedAt    DateTime           @updatedAt @map("updated_at")

  // Relationships
  course          Course            @relation(fields: [courseId], references: [id], onDelete: Cascade)
  studentProgress StudentProgress[]

  @@unique([studentId, courseId])
  @@map("subscriptions")
}

model StudentProgress {
  id             String   @id @default(cuid())
  subscriptionId String   @map("subscription_id")
  lessonId       String   @map("lesson_id")
  studentId      String   @map("student_id")
  completedAt    DateTime @default(now()) @map("completed_at")
  watchedDuration Int     @default(0) @map("watched_duration") // seconds

  // Relationships
  subscription Subscription @relation(fields: [subscriptionId], references: [id], onDelete: Cascade)
  lesson       Lesson       @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@unique([subscriptionId, lessonId])
  @@map("student_progress")
}

model CoachingSession {
  id          String   @id @default(cuid())
  courseId    String   @map("course_id")
  tutorId     String   @map("tutor_id")
  title       String
  description String?
  meetingLink String   @map("meeting_link")
  scheduledAt DateTime @map("scheduled_at")
  duration    Int      @default(60) // minutes
  maxStudents Int      @default(20) @map("max_students")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relationships
  course       Course                    @relation(fields: [courseId], references: [id], onDelete: Cascade)
  participants CoachingSessionParticipant[]

  @@map("coaching_sessions")
}

model CoachingSessionParticipant {
  id               String   @id @default(cuid())
  sessionId        String   @map("session_id")
  studentId        String   @map("student_id")
  joinedAt         DateTime @default(now()) @map("joined_at")

  // Relationships
  session CoachingSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@unique([sessionId, studentId])
  @@map("coaching_session_participants")
}

model Certificate {
  id               String   @id @default(cuid())
  studentId        String   @map("student_id")
  courseId         String   @map("course_id")
  verificationCode String   @unique @map("verification_code")
  issuedAt         DateTime @default(now()) @map("issued_at")
  expiresAt        DateTime? @map("expires_at")

  @@unique([studentId, courseId])
  @@map("certificates")
}

enum AssignmentType {
  QUIZ
  SHORT_ANSWER
  ESSAY
}

enum SubscriptionStatus {
  ACTIVE
  EXPIRED
  TERMINATED
  CANCELLED
}