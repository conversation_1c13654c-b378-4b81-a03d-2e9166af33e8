# Time Course - Online English Course Platform

A comprehensive microservices-based e-learning platform specializing in subscription-based English language test preparation with strict evaluation and coaching systems.

## 🏗️ Architecture Overview

The platform consists of 4 microservices:

1. **Auth & User Service** (Port 3001) - Authentication, user management, and authorization
2. **Course & Learning Service** (Port 3002) - Course content, lessons, assignments, and progress tracking
3. **Payment & Subscription Service** (Port 3003) - Payment processing and subscription management
4. **Reporting & Analytics Service** (Port 3004) - Data aggregation and reporting

## 🚀 Technology Stack

- **Language**: TypeScript (full type safety)
- **Backend**: Express.js with Node.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Firebase Auth
- **Video Streaming**: Bunny.net CDN
- **Payment Gateway**: Xendit
- **Containerization**: Docker & Docker Compose
- **API Gateway**: Nginx
- **CI/CD**: GitHub Actions

## 🎯 Key Features

### Core Differentiating Logic

- **Strict Progress Enforcement**: 3 assignment failures = course termination
- **Mandatory Pacing System**: Automated warnings for students falling behind
- **Free Remedial Coaching**: Automatic eligibility for group coaching after termination
- **Admin-Approved Tutors**: Curated ecosystem with admin-controlled tutor onboarding

### User Roles & Capabilities

#### Students

- Firebase social authentication (Google)
- Dashboard with progress tracking and pacing warnings
- Secure video streaming with embedded assignments
- Real-time assignment grading
- Automatic certificate generation
- Access to coaching sessions after failure

#### Tutors

- Admin-approved account creation
- Course and content management
- Interactive assignment builder with timestamp linking
- Coaching session management
- Student performance analytics

#### Admins

- Complete platform oversight
- User and tutor management
- Course moderation
- System analytics and reporting
- Manual subscription adjustments

## 🏃‍♂️ Quick Start

### Prerequisites

- Node.js 18+
- Docker & Docker Compose
- Firebase project setup
- Bunny.net account
- Xendit account

### Environment Setup

1. Clone the repository:

```bash
git clone <repository-url>
cd time-academy-microservices
```

2. Copy environment file:

```bash
cp .env.example .env
```

3. Configure your `.env` file with:
   - Firebase project credentials
   - Bunny.net API keys
   - Xendit API keys
   - JWT secret

### Running the Application

1. **Development Mode**:

```bash
npm run dev
```

2. **Production Build**:

```bash
npm run build
docker-compose up --build
```

### Database Setup

Each service manages its own database migrations:

```bash
# Run migrations for all services
npm run db:migrate

# Seed databases with initial data
npm run db:seed
```

## 📡 API Endpoints

### Authentication Service (Port 3001)

- `POST /api/auth/verify-token` - Verify Firebase token and create/update user
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user profile
- `GET /api/users` - List users (Admin only)
- `POST /api/admin/tutors` - Create tutor account (Admin only)

### Course Service (Port 3002)

- `GET /api/courses` - List published courses (public)
- `GET /api/courses/:id` - Get course details
- `POST /api/courses` - Create course (Tutor/Admin)
- `POST /api/courses/:id/publish` - Publish/unpublish course
- `GET /api/lessons/:id` - Get lesson content
- `POST /api/assignments/:id/submit` - Submit assignment
- `GET /api/progress/dashboard` - Student dashboard data
- `POST /api/coaching/sessions` - Create coaching session

### Payment Service (Port 3003)

- `POST /api/payments/create-invoice` - Create payment invoice
- `POST /api/payments/webhook` - Xendit webhook handler
- `GET /api/subscriptions/my` - Get user subscriptions
- `POST /api/subscriptions/:id/cancel` - Cancel subscription

### Analytics Service (Port 3004)

- `GET /api/analytics/dashboard` - Admin dashboard analytics
- `GET /api/analytics/courses/:id` - Course performance metrics
- `GET /api/reports/students` - Student progress reports
- `GET /api/reports/revenue` - Revenue analytics

## 🔐 Security Features

- Firebase Authentication integration
- JWT token validation
- Rate limiting (API Gateway + Express)
- CORS configuration
- Security headers (Helmet.js)
- Input validation (express-validator)
- SQL injection prevention (Prisma ORM)
- Video content protection (streaming only, no downloads)

## 🎥 Video Content Security

The platform implements secure video streaming using:

- Bunny.net CDN for video delivery
- HLS/DASH streaming protocols
- No direct download capabilities
- Embedded PDF/PPT viewers without download options

## 📊 Core Business Logic

### Assignment Failure Workflow

1. Student submits assignment
2. Real-time grading (must score 100% to pass)
3. Failed assignment increments `failure_count`
4. At 3 failures: subscription status → `TERMINATED`
5. Student locked out of course content
6. Automatic eligibility for coaching sessions

### Pacing System

- Calculation: `Expected Progress = Days Passed / Course Duration Days × Total Modules`
- Daily checks for students behind schedule
- Dashboard warnings for slow progress
- Configurable pacing thresholds

### Payment Integration

1. Student selects course package
2. Xendit invoice generation
3. Payment completion
4. Webhook verification
5. Automatic subscription activation
6. Access period calculation (start_date + duration_days)

## 🚧 Implementation Status

### ✅ Completed

- Microservices architecture setup
- Docker containerization
- Database schemas and migrations
- Authentication system with Firebase
- Basic API endpoints structure
- API Gateway with Nginx
- CI/CD pipeline setup
- Core business logic implementation
- Security middleware

### 🔄 In Progress / Next Steps

1. **Course Service Completion**:

   - Video upload integration with Bunny.net
   - Assignment submission and grading logic
   - Progress tracking algorithms
   - Coaching session management
   - Certificate generation

2. **Payment Service Implementation**:

   - Complete Xendit integration
   - Webhook handling and verification
   - Subscription lifecycle management
   - Refund processing

3. **Analytics Service Development**:

   - Data aggregation from other services
   - Real-time analytics dashboard
   - Report generation
   - Performance metrics calculation

4. **Frontend Integration**:

   - Student dashboard
   - Course player with video streaming
   - Tutor content management interface
   - Admin panel

5. **Advanced Features**:
   - Real-time notifications
   - Email/SMS alerts for pacing warnings
   - Advanced video analytics
   - Mobile app API optimization

### 📋 Detailed Implementation Checklist

#### Course Service

- [ ] Bunny.net video upload API integration
- [ ] HLS/DASH streaming implementation
- [ ] Assignment question bank system
- [ ] Real-time grading engine
- [ ] Progress calculation algorithms
- [ ] Pacing warning system
- [ ] Certificate generation with verification codes
- [ ] Coaching session booking system

#### Payment Service

- [ ] Complete Xendit API integration
- [ ] Webhook signature verification
- [ ] Subscription status management
- [ ] Payment retry logic
- [ ] Refund processing
- [ ] Invoice generation
- [ ] Currency support

#### Analytics Service

- [ ] Data collection from all services
- [ ] Real-time dashboard metrics
- [ ] Student performance analytics
- [ ] Revenue tracking
- [ ] Course popularity metrics
- [ ] Tutor performance reports
- [ ] Export capabilities

#### Additional Infrastructure

- [ ] Logging system (ELK Stack or similar)
- [ ] Monitoring (Prometheus + Grafana)
- [ ] Error tracking (Sentry)
- [ ] Load balancing
- [ ] Database clustering
- [ ] Backup strategies

## 🤝 Development Guidelines

### Code Organization

- Each service maintains single responsibility
- Shared types in `/services/shared/types`
- Consistent error handling across services
- Comprehensive input validation
- Type-safe database operations

### Testing Strategy

- Unit tests for business logic
- Integration tests for API endpoints
- Database migration testing
- Mock external services in tests
- CI/CD pipeline testing

### Performance Considerations

- Database indexing strategies
- Caching layer implementation
- Video streaming optimization
- API response caching
- Connection pooling

## 📈 Scaling Considerations

The architecture supports horizontal scaling:

- Stateless service design
- Database per service (data isolation)
- Load balancer ready
- Container orchestration (Kubernetes ready)
- Microservice independence

## 🐛 Known Issues & Limitations

1. **Video Streaming**: Bunny.net integration requires additional setup
2. **Real-time Features**: WebSocket implementation needed for live progress updates
3. **Mobile Optimization**: API responses may need optimization for mobile clients
4. **Offline Support**: Not currently implemented
5. **Advanced Analytics**: Requires data aggregation optimization

## 📞 Support & Contribution

This is a comprehensive foundation for the Time Academy platform. The architecture follows microservices best practices and provides a scalable, maintainable solution.

For questions or contributions, please refer to the development team or create issues in the project repository.

---

**Time Academy** - Empowering English learners with structured, accountable learning experiences.
