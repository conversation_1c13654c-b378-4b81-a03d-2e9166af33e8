generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String      @id @default(cuid())
  firebaseUid  String      @unique @map("firebase_uid")
  email        String      @unique
  displayName  String      @map("display_name")
  photoURL     String?     @map("photo_url")
  role         UserRole    @default(STUDENT)
  isActive     Boolean     @default(true) @map("is_active")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // Relationships
  tutorProfile TutorProfile?
  
  @@map("users")
}

model TutorProfile {
  id          String   @id @default(cuid())
  userId      String   @unique @map("user_id")
  bio         String?
  expertise   String[]
  isApproved  Boolean  @default(false) @map("is_approved")
  approvedBy  String?  @map("approved_by")
  approvedAt  DateTime? @map("approved_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("tutor_profiles")
}

model AdminAction {
  id          String         @id @default(cuid())
  adminId     String         @map("admin_id")
  action      AdminActionType
  targetId    String         @map("target_id")
  targetType  String         @map("target_type")
  details     Json?
  createdAt   DateTime       @default(now()) @map("created_at")

  @@map("admin_actions")
}

model UserSession {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  firebaseUid  String   @map("firebase_uid")
  deviceInfo   String?  @map("device_info")
  ipAddress    String?  @map("ip_address")
  lastActivity DateTime @default(now()) @map("last_activity")
  createdAt    DateTime @default(now()) @map("created_at")

  @@map("user_sessions")

  @@unique([userId, firebaseUid])
}

enum UserRole {
  STUDENT
  TUTOR
  ADMIN
}

enum AdminActionType {
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  USER_ACTIVATED
  USER_DEACTIVATED
  TUTOR_APPROVED
  TUTOR_REJECTED
}