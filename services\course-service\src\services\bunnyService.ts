import axios from 'axios';
import FormData from 'form-data';

interface BunnyVideoUploadResponse {
  guid: string;
  libraryId: number;
  title: string;
  dateUploaded: string;
  views: number;
  isPublic: boolean;
  length: number;
  status: number;
  frameworkUrl: string;
  thumbnailUrl: string;
  mp4Url: string;
}

interface BunnyStreamResponse {
  videoLibraryId: number;
  guid: string;
  title: string;
  dateUploaded: string;
  views: number;
  isPublic: boolean;
  length: number;
  status: number;
  frameworkUrl: string;
  thumbnailUrl: string;
  mp4Url: string;
  playlistUrl: string;
  preview: string;
  thumbnail: string;
  mp4: string;
}

class BunnyService {
  private apiKey: string;
  private streamApiKey: string;
  private libraryId: string;
  private baseUrl = 'https://video.bunnycdn.com';
  private streamUrl = 'https://iframe.mediadelivery.net';

  constructor() {
    this.apiKey = process.env.BUNNY_API_KEY!;
    this.streamApiKey = process.env.BUNNY_STREAM_API_KEY!;
    this.libraryId = process.env.BUNNY_LIBRARY_ID!;

    if (!this.apiKey || !this.streamApiKey || !this.libraryId) {
      throw new Error('Bunny.net configuration missing. Please check environment variables.');
    }
  }

  /**
   * Upload video to Bunny.net
   */
  async uploadVideo(
    videoBuffer: Buffer,
    title: string,
    description?: string
  ): Promise<BunnyVideoUploadResponse> {
    try {
      // First, create video entry
      const createResponse = await axios.post(
        `${this.baseUrl}/library/${this.libraryId}/videos`,
        {
          title,
          description: description || '',
        },
        {
          headers: {
            'AccessKey': this.apiKey,
            'Content-Type': 'application/json',
          },
        }
      );

      const videoId = createResponse.data.guid;

      // Upload video file
      await axios.put(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}`,
        videoBuffer,
        {
          headers: {
            'AccessKey': this.apiKey,
            'Content-Type': 'application/octet-stream',
          },
          maxContentLength: Infinity,
          maxBodyLength: Infinity,
        }
      );

      return createResponse.data;
    } catch (error) {
      console.error('Bunny video upload error:', error);
      throw new Error('Failed to upload video to Bunny.net');
    }
  }

  /**
   * Get video details and streaming URLs
   */
  async getVideoDetails(videoId: string): Promise<BunnyStreamResponse> {
    try {
      const response = await axios.get(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}`,
        {
          headers: {
            'AccessKey': this.apiKey,
          },
        }
      );

      const video = response.data;
      
      return {
        ...video,
        playlistUrl: `${this.streamUrl}/embed/${this.libraryId}/${videoId}`,
        preview: `${this.streamUrl}/preview/${this.libraryId}/${videoId}`,
        thumbnail: video.thumbnailUrl,
        mp4: video.mp4Url,
      };
    } catch (error) {
      console.error('Bunny get video error:', error);
      throw new Error('Failed to get video details from Bunny.net');
    }
  }

  /**
   * Generate secure streaming URL with token
   */
  generateSecureStreamUrl(videoId: string, expirationTime?: number): string {
    const expires = expirationTime || Math.floor(Date.now() / 1000) + 3600; // 1 hour default
    const token = this.generateStreamToken(videoId, expires);
    
    return `${this.streamUrl}/embed/${this.libraryId}/${videoId}?token=${token}&expires=${expires}`;
  }

  /**
   * Generate stream token for secure access
   */
  private generateStreamToken(videoId: string, expires: number): string {
    const crypto = require('crypto');
    const data = `${this.libraryId}${videoId}${expires}`;
    return crypto.createHmac('sha256', this.streamApiKey).update(data).digest('hex');
  }

  /**
   * Delete video from Bunny.net
   */
  async deleteVideo(videoId: string): Promise<void> {
    try {
      await axios.delete(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}`,
        {
          headers: {
            'AccessKey': this.apiKey,
          },
        }
      );
    } catch (error) {
      console.error('Bunny delete video error:', error);
      throw new Error('Failed to delete video from Bunny.net');
    }
  }

  /**
   * Update video metadata
   */
  async updateVideo(
    videoId: string,
    updates: { title?: string; description?: string }
  ): Promise<void> {
    try {
      await axios.post(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}`,
        updates,
        {
          headers: {
            'AccessKey': this.apiKey,
            'Content-Type': 'application/json',
          },
        }
      );
    } catch (error) {
      console.error('Bunny update video error:', error);
      throw new Error('Failed to update video metadata');
    }
  }

  /**
   * Get video analytics
   */
  async getVideoAnalytics(videoId: string, dateFrom?: string, dateTo?: string) {
    try {
      const params = new URLSearchParams();
      if (dateFrom) params.append('dateFrom', dateFrom);
      if (dateTo) params.append('dateTo', dateTo);

      const response = await axios.get(
        `${this.baseUrl}/library/${this.libraryId}/videos/${videoId}/statistics?${params}`,
        {
          headers: {
            'AccessKey': this.apiKey,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error('Bunny analytics error:', error);
      throw new Error('Failed to get video analytics');
    }
  }
}

export default new BunnyService();