import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { AppError } from '../middleware/errorHandler';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/subscriptions/my - Get user's subscriptions
router.get('/my', [
  query('status').optional().isIn(['ACTIVE', 'EXPIRED', 'CANCELLED', 'TERMINATED']),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { status, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;
    const studentId = req.user!.id;

    const where: any = { studentId };
    if (status) where.status = status;

    const [subscriptions, total] = await Promise.all([
      prisma.subscription.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.subscription.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/subscriptions/:id - Get subscription details
router.get('/:id', [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findUnique({
      where: { id }
    });

    if (!subscription) {
      throw new AppError('Subscription not found', 404);
    }

    // Check if user owns this subscription or is admin
    if (req.user!.role !== UserRole.ADMIN && subscription.studentId !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }

    res.json({
      success: true,
      data: { subscription }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/subscriptions/:id/cancel - Cancel subscription
router.post('/:id/cancel', [
  param('id').isString().notEmpty(),
  body('reason').optional().isString().isLength({ max: 500 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const subscription = await prisma.subscription.findUnique({
      where: { id }
    });

    if (!subscription) {
      throw new AppError('Subscription not found', 404);
    }

    // Check if user owns this subscription
    if (subscription.studentId !== req.user!.id) {
      throw new AppError('Access denied', 403);
    }

    if (subscription.status !== 'ACTIVE') {
      throw new AppError('Can only cancel active subscriptions', 400);
    }

    // Update subscription
    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        status: 'CANCELLED',
        cancelledAt: new Date(),
        cancelReason: reason || 'Cancelled by user',
        autoRenew: false,
      }
    });

    res.json({
      success: true,
      data: { subscription: updatedSubscription },
      message: 'Subscription cancelled successfully'
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/subscriptions/:id/reactivate - Reactivate cancelled subscription (Admin only)
router.post('/:id/reactivate', requireRole([UserRole.ADMIN]), [
  param('id').isString().notEmpty(),
  body('extendDays').optional().isInt({ min: 1, max: 365 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { extendDays = 0 } = req.body;

    const subscription = await prisma.subscription.findUnique({
      where: { id }
    });

    if (!subscription) {
      throw new AppError('Subscription not found', 404);
    }

    if (subscription.status === 'ACTIVE') {
      throw new AppError('Subscription is already active', 400);
    }

    // Calculate new end date
    const newEndDate = new Date(subscription.endDate);
    if (extendDays > 0) {
      newEndDate.setDate(newEndDate.getDate() + extendDays);
    }

    // Ensure end date is in the future
    if (newEndDate <= new Date()) {
      newEndDate.setDate(new Date().getDate() + 30); // Default 30 days extension
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        status: 'ACTIVE',
        endDate: newEndDate,
        cancelledAt: null,
        cancelReason: null,
      }
    });

    res.json({
      success: true,
      data: { subscription: updatedSubscription },
      message: 'Subscription reactivated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/subscriptions - Get all subscriptions (Admin only)
router.get('/', requireRole([UserRole.ADMIN]), [
  query('status').optional().isIn(['ACTIVE', 'EXPIRED', 'CANCELLED', 'TERMINATED']),
  query('studentId').optional().isString(),
  query('courseId').optional().isString(),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { status, studentId, courseId, page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const where: any = {};
    if (status) where.status = status;
    if (studentId) where.studentId = studentId;
    if (courseId) where.courseId = courseId;

    const [subscriptions, total] = await Promise.all([
      prisma.subscription.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.subscription.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/subscriptions/:id/extend - Extend subscription (Admin only)
router.post('/:id/extend', requireRole([UserRole.ADMIN]), [
  param('id').isString().notEmpty(),
  body('days').isInt({ min: 1, max: 365 }),
  body('reason').optional().isString().isLength({ max: 500 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { days, reason } = req.body;

    const subscription = await prisma.subscription.findUnique({
      where: { id }
    });

    if (!subscription) {
      throw new AppError('Subscription not found', 404);
    }

    const newEndDate = new Date(subscription.endDate);
    newEndDate.setDate(newEndDate.getDate() + days);

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        endDate: newEndDate,
      }
    });

    res.json({
      success: true,
      data: { subscription: updatedSubscription },
      message: `Subscription extended by ${days} days`
    });
  } catch (error) {
    next(error);
  }
});

export default router;