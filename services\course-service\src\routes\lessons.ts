import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { AppError } from '../middleware/errorHandler';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/lessons/:id - Get lesson details
router.get('/:id', [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const lesson = await prisma.lesson.findUnique({
      where: { id },
      include: {
        module: {
          include: {
            course: true
          }
        },
        assignments: {
          select: {
            id: true,
            title: true,
            type: true,
            videoTimestamp: true,
            timeLimit: true,
            passingScore: true,
            questions: true,
          }
        },
        progress: {
          where: { studentId: req.user!.id },
          select: {
            completedAt: true,
            watchedDuration: true,
          }
        }
      }
    });

    if (!lesson) {
      throw new AppError('Lesson not found', 404);
    }

    // Check if user has access to this lesson
    const subscription = await prisma.subscription.findUnique({
      where: {
        studentId_courseId: {
          studentId: req.user!.id,
          courseId: lesson.module.courseId,
        }
      }
    });

    if (!subscription || subscription.status !== 'ACTIVE') {
      throw new AppError('Access denied. Active subscription required.', 403);
    }

    res.json({
      success: true,
      data: { lesson }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/lessons/:id/progress - Update lesson progress
router.post('/:id/progress', [
  param('id').isString().notEmpty(),
  body('watchedDuration').isInt({ min: 0 }),
  body('completed').optional().isBoolean(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { watchedDuration, completed = false } = req.body;

    // Verify lesson exists and user has access
    const lesson = await prisma.lesson.findUnique({
      where: { id },
      include: {
        module: {
          include: { course: true }
        }
      }
    });

    if (!lesson) {
      throw new AppError('Lesson not found', 404);
    }

    const subscription = await prisma.subscription.findUnique({
      where: {
        studentId_courseId: {
          studentId: req.user!.id,
          courseId: lesson.module.courseId,
        }
      }
    });

    if (!subscription || subscription.status !== 'ACTIVE') {
      throw new AppError('Access denied. Active subscription required.', 403);
    }

    // Update or create progress record
    const progress = await prisma.studentProgress.upsert({
      where: {
        subscriptionId_lessonId: {
          subscriptionId: subscription.id,
          lessonId: id,
        }
      },
      create: {
        subscriptionId: subscription.id,
        lessonId: id,
        studentId: req.user!.id,
        watchedDuration,
        completedAt: completed ? new Date() : undefined,
      },
      update: {
        watchedDuration,
        completedAt: completed ? new Date() : undefined,
      }
    });

    // If lesson is completed, update overall course progress
    if (completed) {
      await updateCourseProgress(subscription.id);
    }

    res.json({
      success: true,
      data: { progress },
      message: 'Progress updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/lessons - Create new lesson (Tutor/Admin only)
router.post('/', requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  body('moduleId').isString().notEmpty(),
  body('title').notEmpty().isLength({ min: 3, max: 200 }),
  body('description').optional().isLength({ max: 1000 }),
  body('orderIndex').isInt({ min: 0 }),
  body('videoUrl').optional().isURL(),
  body('videoDuration').optional().isInt({ min: 0 }),
  body('bunnyVideoId').optional().isString(),
  body('materials').optional().isArray(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { moduleId, title, description, orderIndex, videoUrl, videoDuration, bunnyVideoId, materials } = req.body;

    // Verify module exists and user has permission
    const module = await prisma.module.findUnique({
      where: { id: moduleId },
      include: { course: true }
    });

    if (!module) {
      throw new AppError('Module not found', 404);
    }

    // Check if user owns the course (for tutors)
    if (req.user!.role === UserRole.TUTOR && module.course.tutorId !== req.user!.id) {
      throw new AppError('You can only add lessons to your own courses', 403);
    }

    const lesson = await prisma.lesson.create({
      data: {
        moduleId,
        title,
        description,
        orderIndex,
        videoUrl,
        videoDuration,
        bunnyVideoId,
        materials,
      },
      include: {
        module: {
          include: { course: true }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: { lesson },
      message: 'Lesson created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// Helper function to update course progress
async function updateCourseProgress(subscriptionId: string) {
  const subscription = await prisma.subscription.findUnique({
    where: { id: subscriptionId },
    include: {
      course: {
        include: {
          modules: {
            include: {
              lessons: true
            }
          }
        }
      },
      progress: true
    }
  });

  if (!subscription) return;

  const totalLessons = subscription.course.modules.reduce(
    (total, module) => total + module.lessons.length,
    0
  );

  const completedLessons = subscription.progress.length;
  const progressPercentage = totalLessons > 0 ? (completedLessons / totalLessons) * 100 : 0;

  await prisma.subscription.update({
    where: { id: subscriptionId },
    data: { progress: progressPercentage }
  });
}

export default router;