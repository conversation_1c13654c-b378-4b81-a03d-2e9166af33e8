import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../middleware/auth';
import { UserRole, AssignmentType } from '../../shared/types';
import { AppError } from '../middleware/errorHandler';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/assignments/:id - Get assignment details
router.get('/:id', [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    const assignment = await prisma.assignment.findUnique({
      where: { id },
      include: {
        lesson: {
          include: {
            module: {
              include: { course: true }
            }
          }
        },
        submissions: {
          where: { studentId: req.user!.id },
          orderBy: { completedAt: 'desc' },
          take: 1,
        }
      }
    });

    if (!assignment) {
      throw new AppError('Assignment not found', 404);
    }

    // Check if user has access
    const subscription = await prisma.subscription.findUnique({
      where: {
        studentId_courseId: {
          studentId: req.user!.id,
          courseId: assignment.lesson.module.courseId,
        }
      }
    });

    if (!subscription || subscription.status !== 'ACTIVE') {
      throw new AppError('Access denied. Active subscription required.', 403);
    }

    res.json({
      success: true,
      data: { assignment }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/assignments/:id/submit - Submit assignment
router.post('/:id/submit', [
  param('id').isString().notEmpty(),
  body('answers').isArray({ min: 1 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { answers } = req.body;

    const assignment = await prisma.assignment.findUnique({
      where: { id },
      include: {
        lesson: {
          include: {
            module: {
              include: { course: true }
            }
          }
        }
      }
    });

    if (!assignment) {
      throw new AppError('Assignment not found', 404);
    }

    // Check if user has access
    const subscription = await prisma.subscription.findUnique({
      where: {
        studentId_courseId: {
          studentId: req.user!.id,
          courseId: assignment.lesson.module.courseId,
        }
      }
    });

    if (!subscription || subscription.status !== 'ACTIVE') {
      throw new AppError('Access denied. Active subscription required.', 403);
    }

    // Check if already submitted
    const existingSubmission = await prisma.assignmentSubmission.findUnique({
      where: {
        assignmentId_studentId: {
          assignmentId: id,
          studentId: req.user!.id,
        }
      }
    });

    if (existingSubmission) {
      throw new AppError('Assignment already submitted', 400);
    }

    // Grade the assignment
    const { score, passed } = await gradeAssignment(assignment, answers);

    // Create submission
    const submission = await prisma.assignmentSubmission.create({
      data: {
        assignmentId: id,
        studentId: req.user!.id,
        answers,
        score,
        passed,
      }
    });

    // If failed, increment failure count
    if (!passed) {
      const updatedSubscription = await prisma.subscription.update({
        where: { id: subscription.id },
        data: {
          failureCount: { increment: 1 }
        }
      });

      // Check if should terminate (3 failures)
      if (updatedSubscription.failureCount >= 3) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { status: 'TERMINATED' }
        });

        return res.json({
          success: true,
          data: { submission },
          message: 'Assignment submitted. Course access terminated due to 3 failures.',
          terminated: true
        });
      }
    }

    res.json({
      success: true,
      data: { submission },
      message: `Assignment submitted. Score: ${score}%`
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/assignments - Create assignment (Tutor/Admin only)
router.post('/', requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  body('lessonId').isString().notEmpty(),
  body('title').notEmpty().isLength({ min: 3, max: 200 }),
  body('type').isIn(['QUIZ', 'SHORT_ANSWER', 'ESSAY']),
  body('videoTimestamp').optional().isInt({ min: 0 }),
  body('timeLimit').isInt({ min: 1, max: 180 }),
  body('passingScore').isInt({ min: 0, max: 100 }),
  body('questions').isArray({ min: 1 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { lessonId, title, type, videoTimestamp, timeLimit, passingScore, questions } = req.body;

    // Verify lesson exists and user has permission
    const lesson = await prisma.lesson.findUnique({
      where: { id: lessonId },
      include: {
        module: {
          include: { course: true }
        }
      }
    });

    if (!lesson) {
      throw new AppError('Lesson not found', 404);
    }

    // Check if user owns the course (for tutors)
    if (req.user!.role === UserRole.TUTOR && lesson.module.course.tutorId !== req.user!.id) {
      throw new AppError('You can only add assignments to your own courses', 403);
    }

    const assignment = await prisma.assignment.create({
      data: {
        lessonId,
        title,
        type: type as AssignmentType,
        videoTimestamp,
        timeLimit,
        passingScore,
        questions,
      }
    });

    res.status(201).json({
      success: true,
      data: { assignment },
      message: 'Assignment created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// Helper function to grade assignment
async function gradeAssignment(assignment: any, answers: any[]): Promise<{ score: number; passed: boolean }> {
  const questions = assignment.questions as any[];
  let correctAnswers = 0;

  for (let i = 0; i < questions.length; i++) {
    const question = questions[i];
    const studentAnswer = answers[i];

    if (assignment.type === 'QUIZ') {
      // For quiz, check exact match with correct answer
      if (studentAnswer === question.correctAnswer) {
        correctAnswers++;
      }
    } else if (assignment.type === 'SHORT_ANSWER') {
      // For short answer, check if answer contains key terms (simplified)
      const keyTerms = question.keyTerms || [];
      const answerText = studentAnswer.toLowerCase();
      
      let hasAllTerms = true;
      for (const term of keyTerms) {
        if (!answerText.includes(term.toLowerCase())) {
          hasAllTerms = false;
          break;
        }
      }
      
      if (hasAllTerms) {
        correctAnswers++;
      }
    } else if (assignment.type === 'ESSAY') {
      // For essay, this would need more sophisticated grading
      // For now, we'll give partial credit based on word count and key terms
      const wordCount = studentAnswer.split(' ').length;
      const minWords = question.minWords || 100;
      
      if (wordCount >= minWords) {
        correctAnswers++;
      }
    }
  }

  const score = Math.round((correctAnswers / questions.length) * 100);
  const passed = score >= assignment.passingScore;

  return { score, passed };
}

export default router;