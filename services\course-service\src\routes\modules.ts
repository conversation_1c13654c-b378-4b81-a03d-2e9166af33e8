import { Router } from 'express';
import { body, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { AppError } from '../middleware/errorHandler';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// POST /api/modules - Create new module (Tutor/Admin only)
router.post('/', requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  body('courseId').isString().notEmpty(),
  body('title').notEmpty().isLength({ min: 3, max: 200 }),
  body('description').optional().isLength({ max: 1000 }),
  body('orderIndex').isInt({ min: 0 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { courseId, title, description, orderIndex } = req.body;

    // Verify course exists and user has permission
    const course = await prisma.course.findUnique({
      where: { id: courseId }
    });

    if (!course) {
      throw new AppError('Course not found', 404);
    }

    // Check if user owns the course (for tutors)
    if (req.user!.role === UserRole.TUTOR && course.tutorId !== req.user!.id) {
      throw new AppError('You can only add modules to your own courses', 403);
    }

    const module = await prisma.module.create({
      data: {
        courseId,
        title,
        description,
        orderIndex,
      },
      include: {
        course: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      data: { module },
      message: 'Module created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// PUT /api/modules/:id - Update module
router.put('/:id', requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  param('id').isString().notEmpty(),
  body('title').optional().isLength({ min: 3, max: 200 }),
  body('description').optional().isLength({ max: 1000 }),
  body('orderIndex').optional().isInt({ min: 0 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Check if module exists and user has permission
    const existingModule = await prisma.module.findUnique({
      where: { id },
      include: { course: true }
    });

    if (!existingModule) {
      throw new AppError('Module not found', 404);
    }

    // Only allow tutors to edit their own course modules, admins can edit any
    if (req.user!.role === UserRole.TUTOR && existingModule.course.tutorId !== req.user!.id) {
      throw new AppError('You can only edit modules in your own courses', 403);
    }

    const module = await prisma.module.update({
      where: { id },
      data: updates,
      include: {
        course: {
          select: {
            id: true,
            title: true,
          }
        }
      }
    });

    res.json({
      success: true,
      data: { module },
      message: 'Module updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// DELETE /api/modules/:id - Delete module
router.delete('/:id', requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;

    // Check if module exists and user has permission
    const existingModule = await prisma.module.findUnique({
      where: { id },
      include: { 
        course: true,
        lessons: true
      }
    });

    if (!existingModule) {
      throw new AppError('Module not found', 404);
    }

    // Only allow tutors to delete their own course modules, admins can delete any
    if (req.user!.role === UserRole.TUTOR && existingModule.course.tutorId !== req.user!.id) {
      throw new AppError('You can only delete modules in your own courses', 403);
    }

    // Check if module has lessons
    if (existingModule.lessons.length > 0) {
      throw new AppError('Cannot delete module with existing lessons. Delete lessons first.', 400);
    }

    await prisma.module.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Module deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

export default router;