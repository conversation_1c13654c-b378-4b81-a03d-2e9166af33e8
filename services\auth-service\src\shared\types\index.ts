// Shared types across all microservices

export interface User {
  id: string;
  firebaseUid: string;
  email: string;
  displayName: string;
  photoURL?: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  STUDENT = 'STUDENT',
  TUTOR = 'TUTOR',
  ADMIN = 'ADMIN'
}

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  TERMINATED = 'TERMINATED',
  CANCELLED = 'CANCELLED'
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED'
}

export enum AssignmentType {
  QUIZ = 'QUIZ',
  SHORT_ANSWER = 'SHORT_ANSWER',
  ESSAY = 'ESSAY'
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface Course {
  id: string;
  title: string;
  description: string;
  category: string;
  durationDays: number;
  price: number;
  currency: string;
  isPublished: boolean;
  tutorId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subscription {
  id: string;
  studentId: string;
  courseId: string;
  status: SubscriptionStatus;
  failureCount: number;
  startDate: Date;
  endDate: Date;
  progress: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface Assignment {
  id: string;
  lessonId: string;
  title: string;
  type: AssignmentType;
  videoTimestamp?: number;
  timeLimit: number;
  passingScore: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentIntent {
  id: string;
  studentId: string;
  courseId: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  xenditInvoiceId?: string;
  xenditPaymentId?: string;
  createdAt: Date;
  updatedAt: Date;
}