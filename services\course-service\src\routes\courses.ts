import { Router } from 'express';
import { body, query, param, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, requireRole, AuthenticatedRequest } from '../middleware/auth';
import { UserRole } from '../../shared/types';
import { AppError } from '../middleware/errorHandler';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/courses - Get all published courses (public)
router.get('/', [
  query('category').optional().isString(),
  query('search').optional().isString(),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
], handleValidationErrors, async (req, res, next) => {
  try {
    const { category, search, page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const where: any = { isPublished: true };
    
    if (category) {
      where.category = category;
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [courses, total] = await Promise.all([
      prisma.course.findMany({
        where,
        include: {
          _count: {
            select: { subscriptions: true, modules: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
      }),
      prisma.course.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        courses,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/courses/:id - Get course details
router.get('/:id', [
  param('id').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    
    const course = await prisma.course.findUnique({
      where: { id },
      include: {
        modules: {
          include: {
            lessons: {
              include: {
                assignments: {
                  select: {
                    id: true,
                    title: true,
                    type: true,
                    videoTimestamp: true,
                    timeLimit: true,
                    passingScore: true,
                  }
                }
              }
            }
          },
          orderBy: { orderIndex: 'asc' }
        },
        _count: {
          select: { subscriptions: true }
        }
      }
    });

    if (!course) {
      throw new AppError('Course not found', 404);
    }

    // Check if user has access (if authenticated)
    let hasAccess = false;
    if (req.user) {
      const subscription = await prisma.subscription.findUnique({
        where: {
          studentId_courseId: {
            studentId: req.user.id,
            courseId: id,
          }
        }
      });
      hasAccess = subscription?.status === 'ACTIVE';
    }

    res.json({
      success: true,
      data: { 
        course: {
          ...course,
          hasAccess
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/courses - Create new course (Tutor/Admin only)
router.post('/', authenticateToken, requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  body('title').notEmpty().isLength({ min: 3, max: 200 }),
  body('description').notEmpty().isLength({ min: 10, max: 2000 }),
  body('category').notEmpty().isString(),
  body('durationDays').isInt({ min: 1, max: 365 }),
  body('price').isDecimal({ decimal_digits: '0,2' }),
  body('currency').optional().isString().isLength({ min: 3, max: 3 }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { title, description, category, durationDays, price, currency = 'USD' } = req.body;

    const course = await prisma.course.create({
      data: {
        title,
        description,
        category,
        durationDays,
        price,
        currency,
        tutorId: req.user!.id,
      }
    });

    res.status(201).json({
      success: true,
      data: { course },
      message: 'Course created successfully'
    });
  } catch (error) {
    next(error);
  }
});

// PUT /api/courses/:id - Update course
router.put('/:id', authenticateToken, requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  param('id').isString().notEmpty(),
  body('title').optional().isLength({ min: 3, max: 200 }),
  body('description').optional().isLength({ min: 10, max: 2000 }),
  body('category').optional().isString(),
  body('durationDays').optional().isInt({ min: 1, max: 365 }),
  body('price').optional().isDecimal({ decimal_digits: '0,2' }),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const updates = req.body;

    // Check if course exists and user has permission
    const existingCourse = await prisma.course.findUnique({
      where: { id }
    });

    if (!existingCourse) {
      throw new AppError('Course not found', 404);
    }

    // Only allow tutors to edit their own courses, admins can edit any
    if (req.user!.role === UserRole.TUTOR && existingCourse.tutorId !== req.user!.id) {
      throw new AppError('You can only edit your own courses', 403);
    }

    const course = await prisma.course.update({
      where: { id },
      data: updates,
    });

    res.json({
      success: true,
      data: { course },
      message: 'Course updated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// POST /api/courses/:id/publish - Publish/unpublish course
router.post('/:id/publish', authenticateToken, requireRole([UserRole.TUTOR, UserRole.ADMIN]), [
  param('id').isString().notEmpty(),
  body('isPublished').isBoolean(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { id } = req.params;
    const { isPublished } = req.body;

    // Check if course exists and user has permission
    const existingCourse = await prisma.course.findUnique({
      where: { id },
      include: {
        modules: {
          include: {
            lessons: true
          }
        }
      }
    });

    if (!existingCourse) {
      throw new AppError('Course not found', 404);
    }

    // Only allow tutors to publish their own courses, admins can publish any
    if (req.user!.role === UserRole.TUTOR && existingCourse.tutorId !== req.user!.id) {
      throw new AppError('You can only publish your own courses', 403);
    }

    // Validate course has content before publishing
    if (isPublished) {
      const hasLessons = existingCourse.modules.some(module => module.lessons.length > 0);
      if (!hasLessons) {
        throw new AppError('Cannot publish course without lessons', 400);
      }
    }

    const course = await prisma.course.update({
      where: { id },
      data: { isPublished },
    });

    res.json({
      success: true,
      data: { course },
      message: `Course ${isPublished ? 'published' : 'unpublished'} successfully`
    });
  } catch (error) {
    next(error);
  }
});

export default router;