import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Check for expired payment invoices
export async function checkExpiredInvoices() {
  try {
    console.log('Checking for expired payment invoices...');
    
    const expiredInvoices = await prisma.paymentIntent.updateMany({
      where: {
        status: 'PENDING',
        expiresAt: {
          lt: new Date()
        }
      },
      data: {
        status: 'EXPIRED',
        failureReason: 'Invoice expired'
      }
    });

    console.log(`Marked ${expiredInvoices.count} invoices as expired`);
  } catch (error) {
    console.error('Error checking expired invoices:', error);
  }
}

// Check for expired subscriptions
export async function checkExpiredSubscriptions() {
  try {
    console.log('Checking for expired subscriptions...');
    
    const expiredSubscriptions = await prisma.subscription.updateMany({
      where: {
        status: 'ACTIVE',
        endDate: {
          lt: new Date()
        }
      },
      data: {
        status: 'EXPIRED'
      }
    });

    console.log(`Marked ${expiredSubscriptions.count} subscriptions as expired`);
  } catch (error) {
    console.error('Error checking expired subscriptions:', error);
  }
}

// Process pending refunds
export async function processPendingRefunds() {
  try {
    console.log('Processing pending refunds...');
    
    const pendingRefunds = await prisma.refundRequest.findMany({
      where: {
        status: 'APPROVED'
      },
      take: 10 // Process in batches
    });

    for (const refund of pendingRefunds) {
      try {
        // Here you would integrate with Xendit refund API
        // For now, we'll just mark as processed
        await prisma.refundRequest.update({
          where: { id: refund.id },
          data: {
            status: 'PROCESSED',
            processedAt: new Date()
          }
        });

        console.log(`Processed refund ${refund.id} for amount ${refund.amount}`);
      } catch (error) {
        console.error(`Error processing refund ${refund.id}:`, error);
      }
    }
  } catch (error) {
    console.error('Error processing refunds:', error);
  }
}

// Clean up old webhook logs
export async function cleanupWebhookLogs() {
  try {
    console.log('Cleaning up old webhook logs...');
    
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const deletedLogs = await prisma.webhookLog.deleteMany({
      where: {
        createdAt: {
          lt: thirtyDaysAgo
        },
        processed: true
      }
    });

    console.log(`Deleted ${deletedLogs.count} old webhook logs`);
  } catch (error) {
    console.error('Error cleaning up webhook logs:', error);
  }
}