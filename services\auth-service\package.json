{"name": "auth-service", "version": "1.0.0", "description": "Authentication and User Management Service", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "db:migrate": "npx prisma migrate dev", "db:seed": "npx prisma db seed", "db:generate": "npx prisma generate"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "firebase-admin": "^12.0.0", "jsonwebtoken": "^9.0.2", "@prisma/client": "^5.7.0", "bcrypt": "^5.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/bcrypt": "^5.0.2", "typescript": "^5.3.0", "ts-node-dev": "^2.0.0", "prisma": "^5.7.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}