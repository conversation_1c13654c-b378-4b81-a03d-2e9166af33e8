import { Router } from 'express';
import { query, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { authenticateToken, AuthenticatedRequest } from '../middleware/auth';
import { AppError } from '../middleware/errorHandler';

const router = Router();
const prisma = new PrismaClient();

// Validation middleware
const handleValidationErrors = (req: any, res: any, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
    });
  }
  next();
};

// GET /api/progress/dashboard - Get student dashboard data
router.get('/dashboard', async (req: AuthenticatedRequest, res, next) => {
  try {
    const studentId = req.user!.id;

    // Get all active subscriptions with course details
    const subscriptions = await prisma.subscription.findMany({
      where: { studentId },
      include: {
        course: {
          include: {
            modules: {
              include: {
                lessons: {
                  include: {
                    assignments: {
                      include: {
                        submissions: {
                          where: { studentId },
                          select: {
                            score: true,
                            passed: true,
                            completedAt: true,
                          }
                        }
                      }
                    }
                  }
                }
              },
              orderBy: { orderIndex: 'asc' }
            }
          }
        },
        progress: {
          include: {
            lesson: {
              select: {
                id: true,
                title: true,
                moduleId: true,
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Calculate pacing warnings for each subscription
    const dashboardData = subscriptions.map(subscription => {
      const course = subscription.course;
      const totalModules = course.modules.length;
      const totalLessons = course.modules.reduce((sum, module) => sum + module.lessons.length, 0);
      
      // Calculate expected progress based on days passed
      const daysPassed = Math.floor(
        (new Date().getTime() - subscription.startDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      const expectedModules = Math.floor((daysPassed / course.durationDays) * totalModules);
      
      // Calculate actual progress
      const completedLessons = subscription.progress.length;
      const completedModules = course.modules.filter(module => 
        module.lessons.every(lesson => 
          subscription.progress.some(p => p.lessonId === lesson.id)
        )
      ).length;

      // Check for pacing warning
      const isPacingBehind = completedModules < expectedModules && daysPassed > 0;
      
      // Calculate assignment statistics
      let totalAssignments = 0;
      let passedAssignments = 0;
      let failedAssignments = 0;

      course.modules.forEach(module => {
        module.lessons.forEach(lesson => {
          lesson.assignments.forEach(assignment => {
            totalAssignments++;
            if (assignment.submissions.length > 0) {
              if (assignment.submissions[0].passed) {
                passedAssignments++;
              } else {
                failedAssignments++;
              }
            }
          });
        });
      });

      return {
        subscription: {
          id: subscription.id,
          status: subscription.status,
          failureCount: subscription.failureCount,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          progress: subscription.progress,
        },
        course: {
          id: course.id,
          title: course.title,
          category: course.category,
          durationDays: course.durationDays,
        },
        stats: {
          totalModules,
          completedModules,
          totalLessons,
          completedLessons,
          totalAssignments,
          passedAssignments,
          failedAssignments,
          progressPercentage: totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0,
        },
        pacing: {
          daysPassed,
          expectedModules,
          isPacingBehind,
          warningMessage: isPacingBehind 
            ? `You are behind schedule! You should have completed ${expectedModules} modules by now, but you've only completed ${completedModules}.`
            : null,
        }
      };
    });

    // Get coaching sessions for terminated courses
    const terminatedCourseIds = subscriptions
      .filter(sub => sub.status === 'TERMINATED')
      .map(sub => sub.courseId);

    const availableCoachingSessions = terminatedCourseIds.length > 0 
      ? await prisma.coachingSession.findMany({
          where: {
            courseId: { in: terminatedCourseIds },
            isActive: true,
            scheduledAt: { gte: new Date() },
          },
          include: {
            course: {
              select: {
                id: true,
                title: true,
                category: true,
              }
            },
            _count: {
              select: { participants: true }
            }
          },
          orderBy: { scheduledAt: 'asc' }
        })
      : [];

    res.json({
      success: true,
      data: {
        subscriptions: dashboardData,
        availableCoachingSessions,
        summary: {
          totalActiveCourses: subscriptions.filter(s => s.status === 'ACTIVE').length,
          totalTerminatedCourses: subscriptions.filter(s => s.status === 'TERMINATED').length,
          coursesWithPacingWarnings: dashboardData.filter(d => d.pacing.isPacingBehind).length,
        }
      }
    });
  } catch (error) {
    next(error);
  }
});

// GET /api/progress/course/:courseId - Get detailed progress for a specific course
router.get('/course/:courseId', [
  query('courseId').isString().notEmpty(),
], handleValidationErrors, async (req: AuthenticatedRequest, res, next) => {
  try {
    const { courseId } = req.params;
    const studentId = req.user!.id;

    const subscription = await prisma.subscription.findUnique({
      where: {
        studentId_courseId: {
          studentId,
          courseId,
        }
      },
      include: {
        course: {
          include: {
            modules: {
              include: {
                lessons: {
                  include: {
                    assignments: {
                      include: {
                        submissions: {
                          where: { studentId },
                        }
                      }
                    }
                  }
                }
              },
              orderBy: { orderIndex: 'asc' }
            }
          }
        },
        progress: {
          include: {
            lesson: true
          }
        }
      }
    });

    if (!subscription) {
      throw new AppError('Subscription not found', 404);
    }

    res.json({
      success: true,
      data: { subscription }
    });
  } catch (error) {
    next(error);
  }
});

export default router;