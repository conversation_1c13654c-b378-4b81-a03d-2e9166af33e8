import { Request, Response, NextFunction } from "express";
import admin from "firebase-admin";
import axios from "axios";
import { UserRole } from "../shared/types";

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    firebaseUid: string;
    email: string;
    role: UserRole;
  };
}

export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void | Response> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(" ")[1];

    if (!token) {
      return res.status(401).json({
        success: false,
        error: "Access token required",
      });
    }

    // Verify Firebase token
    const decodedToken = await admin.auth().verifyIdToken(token);

    // Get user details from auth service
    try {
      const authServiceUrl =
        process.env.AUTH_SERVICE_URL || "http://auth-service:3001";
      const response = await axios.get(`${authServiceUrl}/api/auth/me`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.data.success) {
        req.user = response.data.data.user;
        next();
      } else {
        throw new Error("User not found in auth service");
      }
    } catch (error) {
      // Fallback to basic user info from Firebase token
      req.user = {
        id: decodedToken.uid,
        firebaseUid: decodedToken.uid,
        email: decodedToken.email!,
        role: UserRole.STUDENT,
      };
      next();
    }
  } catch (error) {
    return res.status(403).json({
      success: false,
      error: "Invalid token",
    });
  }
};

export const requireRole = (roles: UserRole[]) => {
  return (
    req: AuthenticatedRequest,
    res: Response,
    next: NextFunction
  ): void | Response => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: "Authentication required",
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        error: "Insufficient permissions",
      });
    }

    next();
  };
};
